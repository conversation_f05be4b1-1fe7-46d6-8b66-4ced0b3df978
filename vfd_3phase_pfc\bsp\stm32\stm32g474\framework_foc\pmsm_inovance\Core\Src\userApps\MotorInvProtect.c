#include "MotorInvProtectInclude.h"
BEFORE_RUN_PHASE_LOSE_STRUCT gBforeRunPhaseLose;//wujian phase lose added
uint32_t                    gBuffResCnt;    //缓冲电阻保护变量
FAN_CTRL_STRUCT         gFanCtrl;
CBC_PROTECT_STRUCT      gCBCProtect;
struct FAULT_CODE_INFOR_STRUCT_DEF  gError;
OVER_LOAD_PROTECT       gOverLoad;
PHASE_LOSE_STRUCT       gPhaseLose;
LOAD_LOSE_STRUCT        gLoadLose;
IGBT_BREAKE_STRUCT      gIgbtBreake =
{
    .ResetOver = 1,
};
extern uint8_t outputloseCode;
//==================================================
//变频器和电机的过载表
uint16_t const gInvOverLoadTable[10] =      /*表长度10；表步长9%；表最小值:115%；表最大值:196%*/
{
    36000,              //115%变频器电流           1小时过载
    18000,              //124%变频器电流           30分钟过载
    6000,               //133%变频器电流           10分钟过载
    1800,               //142%变频器电流           3分钟过载
    600,                //151%变频器电流           1分钟过载
    200,                //160%变频器电流           20秒过载
    120,                //169%变频器电流           12秒过载
    20,                 //178%变频器电流           6秒过载    改为178% 2S过载
    20,                 //187%变频器电流           2秒过载
    5,                  //196%变频器电流           0.5秒过载   增加一个最大值      2011-10-21-chzq
};

//变频器过载累计量的消除系数
uint32_t const gInvOverLoadDecTable[12] =
{
    (65536L * 60 / 7),  //0%变频器电流    0.7分钟消除过载
    (65536L * 60 / 8),  //10%变频器电流   0.8分钟消除过载
    (65536L * 60 / 9),  //20%变频器电流   0.9分钟消除过载
    (65536L * 60 / 10), //30%变频器电流   1.0分钟消除过载
    (65536L * 60 / 11), //40%变频器电流   1.1分钟消除过载
    (65536L * 60 / 13), //50%变频器电流   1.3分钟消除过载
    (65536L * 60 / 16), //60%变频器电流   1.6分钟消除过载
    (65536L * 60 / 19), //70%变频器电流   1.9分钟消除过载
    (65536L * 60 / 24), //80%变频器电流   2.4分钟消除过载
    (65536L * 60 / 34), //90%变频器电流   3.4分钟消除过载
    (65536L * 60 / 56), //100%变频器电流  5.6分钟消除过载
};

uint16_t const gInvBreakOverLoadTable[5] =
{
    1,                      // 100% 10S最大电流 10s报过载   110%  2s过载
    4,                      // 90% 10S最大电流 40s报过载    120%  0.5s过载
    30,                     // 80% 10S最大电流 300s报过载
    200,                    // 70% 10S最大电流 2000s报过载
    2000,                   // 60% 10S最大电流 20000s报过载
};

#define C_MOTOR_OV_TAB_NUM      14
uint16_t const gMotorOverLoadBaseTable[C_MOTOR_OV_TAB_NUM] =
{
    480,            //115%电机电流  1小时20分钟过载
    240,            //125%电机电流  40分钟过载
    90,             //135%电机电流  15分钟过载
    36,             //145%电机电流  6分钟过载
    24,             //155%电机电流  4分钟过载
    15,             //165%电机电流  2.5分钟过载
    12,             //175%电机电流  2分钟过载

    9,             //185%电机电流  1.5分钟过载
    6,             //195%电机电流  1分钟过载
    5,             //205%电机电流  50S过载
    4,             //215%电机电流  40S过载
    3,             //225%电机电流  30S过载
    2,             //235%电机电流  20S过载
    1              //245%电机电流  10S过载
};
uint16_t gMotorOverLoadTable[C_MOTOR_OV_TAB_NUM] =
{
    48000,              //115%电机电流  1小时20分钟过载
    24000,              //125%电机电流  40分钟过载
    9000,               //135%电机电流  15分钟过载
    3600,               //145%电机电流  6分钟过载
    2400,               //155%电机电流  4分钟过载
    1500,               //165%电机电流  2.5分钟过载
    1200,               //175%电机电流  2分钟过载

    900,               //185%电机电流  1.5分钟过载
    600,               //195%电机电流  1分钟过载
    500,               //205%电机电流  50S过载
    400,               //215%电机电流  40S过载
    300,               //225%电机电流  30S过载
    200,               //235%电机电流  20S过载
    100                //245%电机电流  10S过载
};
#define C_MOTOR_OV_MAX_CUR      4650
#define C_MOTOR_OV_MIN_CUR      1150
#define C_MOTOR_OV_STEP_CUR     100
// #define C_MOTOR_OV_MAX_CUR      1670//wujian
// #define C_MOTOR_OV_MIN_CUR      1150//wujian
// #define C_MOTOR_OV_STEP_CUR     40//wujian

//电机过载累计量的消除系数
uint32_t const gMotorOverLoadDecTable[12] =
{
    (65536L * 60 / 30), //0%电机电流    3.0分钟消除过载
    (65536L * 60 / 40), //10%电机电流   4.0分钟消除过载
    (65536L * 60 / 50), //20%电机电流   5.0分钟消除过载
    (65536L * 60 / 60), //30%电机电流   6.0分钟消除过载
    (65536L * 60 / 70), //40%电机电流   7.0分钟消除过载
    (65536L * 60 / 80), //50%电机电流   8.0分钟消除过?
    (65536L * 60 / 90), //60%电机电流   9.0分钟消除过载
    (65536L * 60 / 100), //70%电机电流   10.0分钟消除过载
    (65536L * 60 / 110), //80%电机电流   11.0分钟消除过载
    (65536L * 60 / 120), //90%电机电流   12.0分钟消除过载
    (65536L * 60 / 130), //100%电机电流  13.0分钟消除过载
};

// //温度曲线表
uint16_t const gTempTableP44X[23] =
{
    624, 614, 603, 590, 576, 561,   //6
    544, 525, 506, 485, 464, 442,   //6
    419, 395, 373, 350, 327, 305,   //6
    284, 264, 244, 226, 208         //5
};
uint16_t const gTempTableP8XX[23] =
{
    475, 451, 426, 400, 374, 348,   //6
    323, 299, 275, 253, 232, 212,   //6
    193, 176, 161, 146, 133, 121,   //6
    110, 100, 91, 83, 76            //5
};
uint16_t const gTempTableBSMXX[23] =
{
    486, 461, 435, 412, 386, 361,   //6
    337, 313, 291, 269, 248, 228,   //6
    209, 193, 176, 161, 148, 135,   //6
    123, 113, 103, 94, 86           //5
};
uint16_t const gTempTableSEMIKON[23] =
{
    558, 519, 480, 451, 418, 392,   //6
    369, 350, 331, 314, 302, 288,   //6
    278, 269, 262, 254, 247, 243,   //6
    237, 233, 229, 226, 224         //5
};
uint16_t const gTempTableWAIZHI[23] =
{
    655, 609, 563, 518, 473, 430,   //6
    389, 350, 314, 282, 251, 224,   //6
    199, 177, 158, 140, 124, 111,   //6
    99, 88, 78, 70, 62              //5
};




/**
  * @brief 根据 ntc_table_5k_3470 转换uint16_t const gTempTableBSMM13[37]
  * @param  R25 = 5K , 1%
            B25/50=3470k B25/85=3640k 1%
  * @retval
  */
uint16_t const  gTempTableBSMM13[41] =
{
    28, 35, 44, 55, 67,   //0-4
    83, 101, 122, 146, 175,   //5-9
    207, 245, 287, 335, 389,   //10-14
    449, 516, 589, 669, 757,    //15-19
    851, 952, 1070, 1187, 1310,        //20-24
    1439, 1573, 1713, 1856, 2018,        //25-29
    2168, 2319, 2472, 2624, 2775,        //30-34
    2925, 3072, 3216, 3357, 3494,              //35-40
    3627                                    //41
};
void OverLoadProtect(void);
int  TemperatureCheck(uint32_t tempAd);
/************************************************************
    变频器保护处理
************************************************************/
//电机过载曲线生成函数
void MotorOverLoadTimeGenerate(void)
{
    u16 i;
    u32 mMotorOverLoadTime;

    for (i = 0; i < 14; i++)
    {
        mMotorOverLoadTime = gMotorOverLoadBaseTable[i] * (u32)gComPar.MotorOvLoad;

        if (mMotorOverLoadTime > 48000) //上限80分钟
        {
            mMotorOverLoadTime = 48000;
        }
        else if (mMotorOverLoadTime < 100) //下限10S
        {
            mMotorOverLoadTime = 100;
        }

        gMotorOverLoadTable[i] = mMotorOverLoadTime;
    }
}

/*************************************************************
    缓冲电阻保护处理

持续的进入欠压状态，认为是缓冲电阻故障
*************************************************************/
void BufferResProtect(void)
{
    if (gBuffResCnt >= 150000L)         //缓冲电阻保护处理
    {
        gError.ErrorCode.all |= ERROR_RESISTER_HOT;
    }
    if (gBuffResCnt > 1)
        gBuffResCnt--;
    gBuffResCnt = __IQsat(gBuffResCnt, 200000L, 0);
}

void InvDeviceControl(void)
{
    TemperatureCheck(gTemperature.TempAD);        //wujian 新增功能           //温度检查
    OverLoadProtect();         //过载保护
    BufferResProtect();
}


/************************************************************
    变频器和电机过载保护
************************************************************/

void OverLoadProtect(void)
{
    uint32_t m_LDeta = 0;
    uint16_t m_Cur, m_Data, m_Index, m_CurBaseInv, m_MotorOvLoad;
    uint16_t m_TorCurBottom, m_TorCurUpper, m_TorCurStep, m_TorCurData;
    uint16_t *m_TorCurLine;
    uint16_t m_Limit;
    long m_OverLoadMargin, m_MaxCurLimit, m_Long;

    m_CurBaseInv = gLineCur.CurBaseInv;

    gOverLoad.FilterInvCur = Filter16(m_CurBaseInv, gOverLoad.FilterInvCur);
    gOverLoad.FilterMotorCur = Filter16(gLineCur.CurPer, gOverLoad.FilterMotorCur);
    gOverLoad.FilterRealFreq = Filter16(gMainCmd.FreqReal, gOverLoad.FilterRealFreq);

    if (gMainStatus.RunStep == STATUS_LOW_POWER)
    {
        gOverLoad.InvTotal.all = 0;
        gOverLoad.MotorTotal.all = 0;

        gOverLoad.Cnt = 0;
        gMainStatus.StatusWord.bit.PerOvLoadInv = 0;
        gMainStatus.StatusWord.bit.PerOvLoadMotor = 0;
        return;
    }

    /************************空压机休眠和堵转处理**********************************/
    if (gOverLoad.OverLoadPreventEnable != 0)
    {
        if ((gOverLoad.InvTotal.half.MSW >= 30000L) && (gMainStatus.RunStep == STATUS_RUN))
        {
            if ((gMainCmd.FreqReal < gMainCmd.XiuMianFreq - 200) && (gMainCmd.XiuMianFreq > 200)) //下限频率减2Hz
            {
                gMainStatus.StatusWord.bit.OverLoadPreventState = 2;
            }
            else
            {
                if (gMainStatus.StatusWord.bit.OverLoadPreventState != 2)
                {
                    gMainStatus.StatusWord.bit.OverLoadPreventState = 1;
                }
            }
        }
    }


    /**************************end************************************************/
    gOverLoad.Cnt++;
    if (gOverLoad.Cnt < 5)
    {
        return;         //每10ms判弦淮?
    }
    gOverLoad.Cnt = 0;

    ////////////////////////////////////////////////////////////////
    //选择过载曲线
    /*2025年3月1日调试过载保护，因为机型电流过大，做运行前缺相功能测试时，无法像HC报出Err10变频器过载故障
    因此为了匹配故障类型此处需要更改保护阈值（因为更改变频器额定电流会影响采样等系统数据，导致无法预知的故障，所以针对机型更改保护阈值）*/
    //if (1 == gInvInfo.GPType)       //G型机过载曲线，恒转矩负载机型
    {
        if (9 == gInvInfo.InvTypeSet) //机型9对应标准电流为803 现需要改成 510
        {
            m_TorCurLine    = (uint16_t *)gInvOverLoadTable;
            m_TorCurBottom  = 730;     //115%变频器电流=510的115%
            m_TorCurUpper   = 1244;     //最大值196%过载    // 更改曲线最大值,2011-10-21-chzq
            m_TorCurStep    = 57;       //wujian (1960-1150)/90=9(对应gInvOverLoadTable从最低到最高需要9个步进)
            m_TorCurData    = 5;        //>=196%,0.5s过载
        }
        else
        {
            m_TorCurLine    = (uint16_t *)gInvOverLoadTable;
            m_TorCurBottom  = 1150;     //115%变频器电流
            m_TorCurUpper   = 1960;     //最大值196%过载    // 更改曲线最大值,2011-10-21-chzq
            m_TorCurStep    = 90;       //wujian (1960-1150)/90=9(对应gInvOverLoadTable从最低到最高需要9个步进)
            m_TorCurData    = 5;        //>=196%,0.5s过载
        }
    }

    m_MaxCurLimit = 5000;       // 500% 保证不受限制
    ////////////////////////////////////////////////////////////////
    //开始判断变频器的过载
    m_Cur = ((uint32_t)gOverLoad.FilterInvCur * 1000L) >> 12;

    if (m_Cur < m_TorCurBottom)
    {
        if (gOverLoad.InvTotal.half.MSW < 10)
        {
            gOverLoad.InvTotal.all = 0;
        }
        else if (gMainStatus.RunStep == STATUS_STOP)
        {
            gOverLoad.InvTotal.all -= gInvOverLoadDecTable[0];
        }
        else if (m_Cur < 1000)      /*小于变频器额定电流，按照当前电流大小消除过载累计量*/
        {
            gOverLoad.InvTotal.all -= gInvOverLoadDecTable[m_Cur / 100 + 1];
        }
    }
    else
    {
        if (gOverLoad.FilterRealFreq < 500)
        {
            m_Data = gOverLoad.FilterRealFreq * 13 + 26214;
            m_Cur  = (((uint32_t)m_Cur) << 15) / m_Data;
            if (gOverLoad.OverLoadPreventEnable != 0)
            {
                if (gOverLoad.InvTotal.half.MSW > 10800)    //当过载30%，低频将过载限制在170%
                {
                    m_MaxCurLimit = (1700UL * (uint32_t)m_Data) >> 15;
                }
            }
        }
        if (m_Cur >= m_TorCurUpper)
        {
            m_Data = m_TorCurData;
        }
        else
        {
            m_Index = (m_Cur - m_TorCurBottom) / m_TorCurStep;
            m_Data = *(m_TorCurLine + m_Index) -
                     (((long)(*(m_TorCurLine + m_Index)) - (*(m_TorCurLine + m_Index + 1))) *
                      (long)(m_Cur - m_TorCurBottom - m_Index * m_TorCurStep)) / m_TorCurStep;
        }
        m_LDeta = ((uint32_t)3600 << 16) / (uint16_t)m_Data;
        gOverLoad.InvTotal.all += m_LDeta;

        if (gOverLoad.InvTotal.half.MSW >= 36000)
        {
            gOverLoad.InvTotal.half.MSW = 36000;
            //AddOneError(ERROR_INV_OVER_LOAD,1);

            gMainStatus.StatusWord.bit.PerOvLoadInv = 0;
            // gError.ErrorCode.all |= ERROR_INV_OVER_LAOD;
            // gError.ErrorInfo[1].bit.Fault1 = 2;
        }
    }

    gOverLoad.TotalPercent = (uint32_t)gOverLoad.InvTotal.half.MSW * 100L / 36000L;
    /*****************防过载处理***********************************/
    if ((gOverLoad.OverLoadPreventEnable != 0) && (gMainStatus.StatusWord.bit.OverLoadPreventState != 2)) //   报休眠故障后不限制电流
    {
        m_OverLoadMargin = 30000L - (long)gOverLoad.InvTotal.half.MSW;
        if (m_OverLoadMargin < 720)
        {
            m_Limit = 1800;
            if (m_OverLoadMargin < 400)
            {
                m_Limit = 1700;
            }
            m_Long = 1350 + (m_OverLoadMargin << 1);
            if (m_MaxCurLimit > m_Long)
            {
                m_MaxCurLimit = m_Long;
            }
            m_MaxCurLimit = __IQsat(m_MaxCurLimit, m_Limit, 950);
        }
        //gLineCur.OverLoadMargin = m_OverLoadMargin; //测试用
        gLineCur.MaxCurLimit = m_MaxCurLimit;
        gLineCur.MaxCurLimit =  __IQsat(gLineCur.MaxCurLimit, 1800, 250);
    }
    else
    {
        gLineCur.MaxCurLimit = 1800;
    }
    /****************************end******************************/
    //变频器过载预报警处理
    if (((gOverLoad.InvTotal.all + m_LDeta * 1000UL) >> 16) > 36000)
    {
        gMainStatus.StatusWord.bit.PerOvLoadInv = 1;
    }
    else
    {
        gMainStatus.StatusWord.bit.PerOvLoadInv = 0;
    }

    MotorOverLoadTimeGenerate();//电机过载曲线生成函数
    ////////////////////////////////////////////////////////////////
    //开始判断电机的过载
    if (gSubCommand.bit.MotorOvLoad == 0)
    {
        gOverLoad.MotorTotal.all = 0;
        gMainStatus.StatusWord.bit.PerOvLoadMotor = 0;
        return;
    }

    m_Cur = ((uint32_t)gOverLoad.FilterMotorCur * 1000L) >> 12;
    //m_Cur = ((uint32_t)m_Cur * 100L)/100;//gComPar.MotorOvLoad;          // 根据过载保护系数计算出保护电流，
    //然后用该保护电流查询过载保护曲线
    m_LDeta = (uint32_t)m_Cur * (uint32_t)gMotorInfo.CurBaseCoff;
    if (m_LDeta >= (C_MOTOR_OV_MAX_CUR * 256L))
    {
        m_Cur = C_MOTOR_OV_MAX_CUR;
    }
    else
    {
        m_Cur = m_LDeta >> 8;
    }

    m_MotorOvLoad = __IQsat(gComPar.MotorOvLoad, 150, 100);

    if (m_Cur < (u16)((u32)m_MotorOvLoad * C_MOTOR_OV_MIN_CUR / 100UL))
    {
        if (gOverLoad.MotorTotal.half.MSW < 10)
        {
            gOverLoad.MotorTotal.all = 0;
        }
        else if (gMainStatus.RunStep == STATUS_STOP)
        {
            gOverLoad.MotorTotal.all -= gMotorOverLoadDecTable[0];
        }
        else if (m_Cur < m_MotorOvLoad * 10)                /*小于100%额定电流按照电流消除电机过载*/
        {
            gOverLoad.MotorTotal.all -= gMotorOverLoadDecTable[m_Cur / m_MotorOvLoad + 1];
        }
    }
    else
    {
        if (m_Cur >= C_MOTOR_OV_MAX_CUR)
        {
            m_Data = gMotorOverLoadTable[C_MOTOR_OV_TAB_NUM - 1];
        }
        else
        {
            m_Index = (m_Cur - (u16)((u32)m_MotorOvLoad * C_MOTOR_OV_MIN_CUR / 100UL)) / C_MOTOR_OV_STEP_CUR;
            m_Data = gMotorOverLoadTable[m_Index] -
                     ((long)(gMotorOverLoadTable[m_Index] - gMotorOverLoadTable[m_Index + 1]) *
                      (long)(m_Cur - (u16)((u32)m_MotorOvLoad * C_MOTOR_OV_MIN_CUR / 100UL) - m_Index * C_MOTOR_OV_STEP_CUR)) / C_MOTOR_OV_STEP_CUR;
        }
        m_LDeta = ((uint32_t)3600 << 16) / (uint16_t)m_Data;
        gOverLoad.MotorTotal.all += m_LDeta;

        if (gOverLoad.MotorTotal.half.MSW > 36000)
        {
            gOverLoad.MotorTotal.half.MSW = 36000;
            // gMainStatus.StatusWord.bit.PerOvLoadMotor = 0;
            // gError.ErrorCode.all |= ERROR_MOTOR_OVER_LOAD;
        }
    }

    //电机过载预报�处�?
    if (gOverLoad.MotorTotal.half.MSW > gComPar.PerMotorOvLoad * 360)
    {
        gMainStatus.StatusWord.bit.PerOvLoadMotor = 1;
    }
    else
    {
        gMainStatus.StatusWord.bit.PerOvLoadMotor = 0;
    }
}
/************************************************************
MD380温度检查,说明如下:

1、机型小于等于10       (2.2Kw 以?
    温度曲线:        1      TABLE_P44X
                2       TABLE_P8XX
                3       TABLE_SEMIKON
                4       TABLE_BSMXX

2、机型在（11～18之间 (11Kw 到 30Kw)
    温度曲线:        1      TABLE_BSMXX
                2       TABLE_P44X
                3~4     TABLE_SEMIKON

3、机型在（19～26之间） (37Kw 到 200Kw,并且包含37, 不包含200)
    温度采样外置：       TABLE_WAIZHI

4、机型大于等于27        TABLE_BSMXX

5、机型大于等于27时，温度采样电路不同，需要进行3.3V和3V的转换；

6、机型大于等于19的85度保护；其他机型95度保护；

表格排列方式：每4度作一个数据，起始地址数据为12度，数据值为AD采样值
AD采样值：AD_RESULT>>6
************************************************************/

/*
wujian 需要根据自己实际情况更改温度函数，
*/
int  TemperatureCheck(uint32_t tempAd)
{
    uint16_t     *m_pTable, *m_ErrTempTable;
    __IO long    m_TempAD, m_IndexLow, m_IndexHigh, m_Index;
    __IO long    m_ErrTemp;
    __IO long    m_RecovTemp;
    __IO uint16_t    m_LimtCnt;
    int    mType, m_TempMax, m_TempMin;

    //===========wujian
    m_ErrTemp = 110;//protect:110degree decrease load:105degree  recove:85degree摄氏度 保护   (105-15)=90---降低载波频率
    m_RecovTemp = 85;
    mType = gInvInfo.InvTypeSet; //机型9对应PMSM13机型
    gTemperature.OverTempPoint = 105;//m_ErrTemp;// PMSM13机型默认过温点
    //===========================
    // 硬件采样，以及采样电路的修正//wujian 电阻表  ntc_table_5k_3470
    m_TempAD = tempAd >> 4; //温度AD最高采样为12位
    m_pTable = (uint16_t *)gTempTableBSMM13;
    // 开始查询温度表
    m_IndexLow  = 0;
    m_IndexHigh = 40;
    m_Index = 20;//温度表格二分法的中简值
    m_TempMax = 125;//最高温度
    m_TempMin = -35;//最低温度
    if (m_TempAD <= m_pTable[m_IndexLow])
    {
        mType = m_TempMin * 16;
    }
    else if (m_TempAD >= m_pTable[m_IndexHigh])
    {
        mType = m_TempMax * 16;//Q4
    }
    else
    {
        m_LimtCnt = 0;
        while (m_LimtCnt < 8) //8为二分法查询最多需要次数
        {
            m_LimtCnt++;                    //避免死循环
            if (m_TempAD == m_pTable[m_Index])
            {
                mType = (m_Index << 6) + m_TempMin * 16;
                break;
            }
            else if (m_IndexLow + 1 == m_IndexHigh)
            {
                mType = (m_IndexLow << 6) + m_TempMin * 16
                        + ((m_TempAD - (long)m_pTable[m_IndexLow]) << 6)
                        / ((long)m_pTable[m_IndexHigh] - (long)m_pTable[m_IndexLow]);
                break;
            }

            if (m_TempAD < m_pTable[m_Index])
            {
                m_IndexHigh = m_Index;
            }
            else
            {
                m_IndexLow = m_Index;
            }
            m_Index = (m_IndexLow + m_IndexHigh) >> 1;
        }
    }
    if (abs(mType - gTemperature.TempBak) >= 8)         //温度变化超过0.5度才赋值
    {
        gTemperature.TempBak = mType;
        gTemperature.Temp = mType >> 4;
    }

// 开始作温度判断和报警处理
    // gTemperature.ErrCnt++;
    if ((gTemperature.Temp <= m_RecovTemp)
            || vfd.diag.dc_90) //clear temp err bit when sensor err
    {
        gTemperature.ErrCnt = 0;
        gError.ErrorCode.all &= (~0x0000100);
    }
    else if (gTemperature.Temp >= m_ErrTemp)
    {
        if (gTemperature.ErrCnt >= 5)
        {
            //gError.ErrorCode.all |= ERROR_INV_TEMPERTURE;       //过热报警
        }
        else
        {
            gTemperature.ErrCnt ++;
        }
    }
    else
    {
        gTemperature.ErrCnt = 0;
    }



    return gTemperature.Temp;
}


//#include "SEGGER_RTT.h"
//=================================================================
void ShortGnd_PhaseLoseICal(void);
void ShortGnd_ADC_Over_isr(void);//interrupt
void ResetPhaseLoseDetect(void);
void BeforeRunShortGndDetect(void);
void BeforeRunOutputPhaseLoseDetect(void);

uint8_t lose_phase_uvw = 0;
__IO uint32_t save_phaselose_max = 0;
__IO uint32_t save_phaselose_min = 0;
void OutputPhaseLoseDetect(void)
{
    Uint m_U, m_V, m_W;
    Uint m_Max, m_Min, uvw_Max1, uvw_Max2;
    if ((gSubCommand.bit.OutputLost == 0) ||
            (gMainCmd.Command.bit.StartDC == 1) ||      //直流制动状态下不�?��?
            (gMainCmd.Command.bit.StopDC  == 1) ||
            (gMainStatus.RunStep != STATUS_RUN) ||
            (gMainCmd.FreqReal < 200) ||                 //0.8Hz以下不�?��?
            (gMainCmd.FreqReal > 50000))
    {
        OutputLoseReset();
        return;
    }
    // if((gPhaseLose.Time < 4000000)&&(gPhaseLose.Cnt<400))    // 20�?电流周期且至少需�?200ms后判�?输出缺相
    if ((gPhaseLose.Cnt < 400)) // 20�?电流周期且至少需�?200ms后判�?输出缺相
    {
        return;
    }
    m_U = gPhaseLose.TotalU / gPhaseLose.Cnt;
    m_V = gPhaseLose.TotalV / gPhaseLose.Cnt;
    m_W = gPhaseLose.TotalW / gPhaseLose.Cnt;

    m_Max = (m_U > m_V) ? m_U : m_V;
    m_Min = (m_U < m_V) ? m_U : m_V;
    m_Max = (m_Max > m_W) ? m_Max : m_W;
    m_Min = (m_Min < m_W) ? m_Min : m_W;

    if (m_U == m_Min)
    {
        uvw_Max1 = m_V;
        uvw_Max2 = m_W;
    }
    else if (m_V == m_Min)
    {
        uvw_Max1 = m_U;
        uvw_Max2 = m_W;
    }
    if (m_W == m_Min)
    {
        uvw_Max1 = m_U;
        uvw_Max2 = m_V;
    }
    save_phaselose_max = m_Max;
    save_phaselose_min = m_Min;
    m_Min = Max(m_Min, 1);

    OutputLoseReset();
    if ((m_Max > 500) && (uvw_Max1 / m_Min > 3) && (uvw_Max2 / m_Min > 3))
    {

        if (save_phaselose_min == m_U) lose_phase_uvw = 1;
        if (save_phaselose_min == m_V) lose_phase_uvw = 2;
        if (save_phaselose_min == m_W) lose_phase_uvw = 3;
        gError.ErrorCode.all |= ERROR_OUTPUT_LACK_PHASE;
        gError.ErrorInfo[1].bit.Fault4 = 1;
        outputloseCode = 9;
    }
    if (gPhaseLose.Time < 4000000)
    {
        if ((m_Max > 500) && (m_Max / m_Min > 3))
        {
            if (save_phaselose_min == m_U) lose_phase_uvw = 1;
            if (save_phaselose_min == m_V) lose_phase_uvw = 2;
            if (save_phaselose_min == m_W) lose_phase_uvw = 3;
            gError.ErrorCode.all |= ERROR_OUTPUT_LACK_PHASE;
            gError.ErrorInfo[1].bit.Fault4 = 1;
            outputloseCode = 10;

        }
    }

}

void OutputLoseAdd(void)        //输出缺相检测累加电流�?�理
{
    u16 index;
    gPhaseLose.TotalU += abs(gIUVWQ24.U >> 12);
    gPhaseLose.TotalV += abs(gIUVWQ24.V >> 12);
    gPhaseLose.TotalW += abs(gIUVWQ24.W >> 12);

    gPhaseLose.Time += gMainCmd.FreqReal;

    index = gPhaseLose.Cnt % 16;
    gPhaseLose.Cnt ++;
}

void OutputLoseReset(void)      //输出缺相检测�?�位寄存器�?�理
{
    gPhaseLose.Cnt = 0;
    gPhaseLose.TotalU = 0;
    gPhaseLose.TotalV = 0;
    gPhaseLose.TotalW = 0;
    gPhaseLose.Time   = 0;
}
//-----------wujian added for shortGND and phase lose befor run-----
#define CHARGEPUMP_FIRST     1
#define SHORT_PHASE_JUMP      2
#define SHORT_GND_CHECK          3
#define CHARGEPUMP_PHEASE     4
#define WAVE_FOR_PHEASE      5
#define PHEASE_CHECK         6
#define FINISH_CHECK           7

u16 ChargePumpUsed = 0; //PVPB09 set 0 to diasble ChargePumpUsed
u16 ChargePumpFlag = 0; //wujian �?举电容充�? 至少4Ms
u16 ChargePumpTimeout;
u16 ShortGndCheckOk = 0;
//因为底层刹车逻辑把gMainCmd.Command.bit.Start清除了，导致在该函数在做U相对地时(VW相有负载电感，不会触发刹车)
//无法报出对地短路故障，而且该故障为锁死故障，因此在此处用CheckGndWaitBreakFlag进行打补丁报故障
u16 CheckGndWaitBreakFlag;

u16 PumpChargeFunction(void)
{
    u16 PumpOk;
    Ulong m_Period;
    PumpOk = 0;
    if (ChargePumpUsed) //系统是否有用电荷泵
    {
        if (ChargePumpFlag == 0)
        {
            ChargePumpFlag = 1;
            ChargePumpTimeout = 0;
            CheckGndWaitBreakFlag = 1; //当U相接地时单独开A-也会形成电流通道从而导致刹车信号触发，此种情况也归结于接地短路
            gIPMInitPos.PWMTs     = 0x7FB4;//20*DSP_CLOCK;//gIPMInitPos.InitPWMTs;
            SynInitPosDetSetTs();
            // SetPwmMode(0x6060UL, 0x6060UL);
            m_Period = __HAL_TIM_GET_AUTORELOAD(&htim1);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, m_Period);// A-  给电荷泵充电
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, m_Period);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
            // SET_BIT((&htim1)->Instance->CR1, TIM_CR1_ARPE);;//wujian enable ARR preload
            // __HAL_TIM_ENABLE_OCxPRELOAD(&htim1,TIM_CHANNEL_1);//wujian enable OC preload
            // __HAL_TIM_ENABLE_OCxPRELOAD(&htim1,TIM_CHANNEL_2);//wujian enable OC preload
            // __HAL_TIM_ENABLE_OCxPRELOAD(&htim1,TIM_CHANNEL_3);//wujian enable OC preload
            EnableDrive();
        }
        else if (++ChargePumpTimeout > 1) //A-开启4MS后（2MS任务）认为电荷泵已充满
        {

            DisableDrive();
            m_Period = __HAL_TIM_GET_AUTORELOAD(&htim1);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, m_Period);//全关 准�?�缺相�?��?
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, m_Period);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
            PumpOk = 1;
            ChargePumpFlag = 0;
            ChargePumpTimeout = 0;

        }
    }
    else//驱动没有电压�?
    {
        PumpOk = 1;
        ChargePumpFlag = 0;
    }
    return (PumpOk);

}
void ResetLosePhaseErrbit(void)
{
    gError.ErrorCode.all &= ~ERROR_OUTPUT_LACK_PHASE;
}
int32_t gLosePhase_maxCur[3] = {0};
u16 UVphaseCheck_Over;
u16 UVphaseLoseFlg;
uint8_t  shortGnd_times = 0;
// u16 PhaseLose_UW;
void OutputPhaseLoseAndShortGndDetect(void)     //wujian--- 重构该函数
{
    u16 PumpState;
    uint16_t Data1, Data2;
    Ulong m_Period;
    if ((gError.ErrorCode.all != 0)      ||
            (vfd.bit.hard_stop)          ||
            (vfd.bit.close_detect)       ||
            ((gMainCmd.Command.bit.Start == 0) && (gMainStatus.RunStep != STATUS_SHORT_GND))) //wujian 没有�?机指令只允�?��?�地�?�?测试功能进入
    {
        gMainStatus.SubStep = 1;
        gBforeRunPhaseLose.CheckOverFlag = 1;
        //增加报故障逻辑
        if (CheckGndWaitBreakFlag && gShortGnd.ocFlag && !vfd.bit.close_detect)
        {
            // CheckGndWaitBreakFlag=0;
            gError.ErrorInfo[2].bit.Fault4 = 3; //�?件过�?
            gError.ErrorCode.all |= ERROR_SHORT_EARTH;
            outputloseCode = 14;
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);//all off
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
        }
        ResetPhaseLoseDetect();
        TurnToStopStatus();
        return;
    }


    switch (gMainStatus.SubStep)
    {
    case CHARGEPUMP_FIRST:
        
        UVphaseCheck_Over = 0;
        UVphaseLoseFlg = 0;
        gBforeRunPhaseLose.Counter  = 0;
        gBforeRunPhaseLose.OverFlag = 0;

        //  gMainStatus.PrgStatus.bit.PWMDisable = 1; // 禁�??下溢�?�?发波
        // DisableDrive();
        // �?动前对地�?�?检测及�?动前输出缺相检测电流阀值的计算
        // 默�?�为2048(Q12格式)，但必须小于80%变�?�器额定电流，小�?2倍电机�?�定电流
        Data1 = ((uint32_t)gInvInfo.InvCurrent * 3277) / gMotorInfo.Current;    // 80%变�?�器额定电流, Q12
        Data2 = ((uint32_t)gInvInfo.InvCurrent << 12) / gMotorInfo.Current;     // 变�?�器额定电流, Q12
        gBforeRunPhaseLose.CurComperCoff = (Data1 < 2048) ? Data1 : 2048;       // min(50%基值电�?, 80% 变�?�器额定电流)
        gBforeRunPhaseLose.CurComperCoffLimit = (Data2 < 3277) ? Data2 : 3277;  // min(80%基值电�?, 变�?�器额定电流)

        if (gExtendCmd.bit.ShortGndBeforeRun)
        {
            gBforeRunPhaseLose.ShortGndDetectFlag = 1;
        }
        SetADCEndIsr(ShortGnd_ADC_Over_isr);//wujian set adc isr
        PumpState = PumpChargeFunction();
        if (PumpState)
            gMainStatus.SubStep = SHORT_PHASE_JUMP;

        break;
    case SHORT_PHASE_JUMP://根据条件判断 缺相发波或者对地短路检测发波
        if ((gBforeRunPhaseLose.OverFlag != 1) &&
                (gShortGnd.ocFlag == 0))
        {
            EnableDrive();
        }
        if (gExtendCmd.bit.ShortGndBeforeRun != 1)  // 启动前不检测对地短路, 直接检测输出缺相, PWM寄存器配置
        {
            UVphaseCheck_Over = 0;
            UVphaseLoseFlg = 0;
            CheckGndWaitBreakFlag = 0;
            m_Period = __HAL_TIM_GET_AUTORELOAD(&htim1);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, m_Period);//A+ B-
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, m_Period);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, m_Period);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
            gMainStatus.SubStep = PHEASE_CHECK; //�?动前不�?�测�?�地�?�?, 直接检测输出缺�?,
            gBforeRunPhaseLose.DetecTimes++;
        }
        else // �?动前对地�?�?检�?, 标志位�?�置, PWM寄存器配�?
        {
            gBforeRunPhaseLose.ShortGndDetectFlag = 1;
            ShortGndCheckOk = 0;

            m_Period = __HAL_TIM_GET_AUTORELOAD(&htim1);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, m_Period);//A+
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, m_Period);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
            CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
            // SetADCEndIsr(ShortGnd_ADC_Over_isr);//wujian set adc isr
            CheckGndWaitBreakFlag = 1;
            gMainStatus.SubStep = SHORT_GND_CHECK;
            gBforeRunPhaseLose.DetecTimes++;
        }
        break;
    case SHORT_GND_CHECK://对地检�?
        CheckGndWaitBreakFlag = 1;
        if (gExtendCmd.bit.ShortGndBeforeRun == 1)
        {
            BeforeRunShortGndDetect();  // �?动前对地�?�?检测判�?, �?完成检测前, �?�?执�??
            gMainStatus.StatusWord.bit.ShortGndOver = 1;
        }
        if ((gSubCommand.bit.OutputLostBeforeRun == 1) && (ShortGndCheckOk == 1)) //需要�?�测缺相并且�?�地�?�?通过
        {
            gBforeRunPhaseLose.DetecTimes++;
            gMainStatus.SubStep = CHARGEPUMP_PHEASE; //检测成功跳�?到电荷泵充电，没有�?�测成功直接跳出状�?
        }
        else if ((STATUS_SHORT_GND == gMainStatus.RunStep) &&
            (gSubCommand.bit.OutputLost == 1) && (ShortGndCheckOk == 1)) //需要�?�测缺相并且�?�地�?�?通过
        {
            gBforeRunPhaseLose.DetecTimes++;
            gMainStatus.SubStep = CHARGEPUMP_PHEASE; //检测成功跳�?到电荷泵充电，没有�?�测成功直接跳出状�?
        }
        else
        {
            gMainStatus.SubStep = FINISH_CHECK; //如果没有缺相检测需求则直接跳出
        }
        break;
    case CHARGEPUMP_PHEASE://电荷�?


        PumpState = PumpChargeFunction();
        if (PumpState)
            gMainStatus.SubStep = WAVE_FOR_PHEASE;
        break;
    case WAVE_FOR_PHEASE://输出缺相发波

        UVphaseCheck_Over = 0;
        UVphaseLoseFlg = 0;
        CheckGndWaitBreakFlag = 0;
        gBforeRunPhaseLose.maxIU    = 0;
        gBforeRunPhaseLose.maxIV    = 0;
        gBforeRunPhaseLose.maxIW    = 0;

        EnableDrive();
        m_Period = __HAL_TIM_GET_AUTORELOAD(&htim1);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, m_Period);//A+ B-
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, m_Period);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, m_Period);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
        gMainStatus.SubStep = PHEASE_CHECK;
        break;
    case PHEASE_CHECK://输出缺相检�?

        CheckGndWaitBreakFlag = 0;
        gBforeRunPhaseLose.Counter++;
        BeforeRunOutputPhaseLoseDetect(); //    �?动前输出缺相检测判�?
        if (gBforeRunPhaseLose.CheckOverFlag)
        {
            DisableDrive();
            gMainStatus.SubStep = FINISH_CHECK;
            
            gLosePhase_maxCur[1] = gBforeRunPhaseLose.maxIU;
            lose_phase_uvw = UVphaseLoseFlg ? 2 : 0;
            
            if (lose_phase_uvw)
            {
                gError.ErrorCode.all |= ERROR_OUTPUT_LACK_PHASE;//报输出缺�?
                gError.ErrorInfo[1].bit.Fault4 = 3;
                gBforeRunPhaseLose.Counter = 0;
                outputloseCode = 11;
            }

        }
        // else if (gBforeRunPhaseLose.Counter >= 6)
        else if (gBforeRunPhaseLose.Counter >=1)
        {
            if (gBforeRunPhaseLose.UWPhaseLoseFlag == 1) //UW 已完成发波则直接判断并跳出
            {
                DisableDrive();
                lose_phase_uvw = UVphaseLoseFlg ? 1 : 3;
                gBforeRunPhaseLose.CheckOverFlag = 1;
                gMainStatus.SubStep = FINISH_CHECK;
                gLosePhase_maxCur[1] = gBforeRunPhaseLose.maxIU;
                if (gMainStatus.ErrFlag.bit.OvCurFlag == 0)
                {
                    gError.ErrorCode.all |= ERROR_OUTPUT_LACK_PHASE;//报输出缺�?
                    gError.ErrorInfo[1].bit.Fault4 = 3;
                    gBforeRunPhaseLose.Counter = 0;
                    outputloseCode = 12;
                }
            }
            else
            {

                UVphaseCheck_Over = 1;
                if (gBforeRunPhaseLose.OverFlag == 0)
                {
                     UVphaseLoseFlg = 1;
                     
                }    
                // gBforeRunPhaseLose.Counter=0;
            }

            // ResetPhaseLoseDetect();
        }
        break;
    case FINISH_CHECK:
    default:
        CheckGndWaitBreakFlag = 0;
        ChargePumpTimeout = 0;
        gBforeRunPhaseLose.CheckOverFlag = 1;
        // gMainStatus.SubStep=FINISH_CHECK;
        gMainStatus.SubStep = CHARGEPUMP_FIRST;
        gMainStatus.RunStep = STATUS_STOP;
        ResetPhaseLoseDetect();
        break;

    }

}


void ResetPhaseLoseDetect(void)
{
    DisableDrive();

    gBforeRunPhaseLose.maxIU = 0;
    gBforeRunPhaseLose.maxIV = 0;
    gBforeRunPhaseLose.maxIW = 0;
    gBforeRunPhaseLose.Counter = 0;
    gBforeRunPhaseLose.UWPhaseLoseFlag = 0;
    gBforeRunPhaseLose.ShortGndDetectFlag = 0;
    gBforeRunPhaseLose.PowerOnLosePhaseDetectFlag = 0;
    gShortGnd.ocFlag = 0;//wujian


    InitSetPWM();            //wujian 参考参数识�?完成           //恢�?�修改的寄存器配�?
    ResetADCEndIsr(); // 复位主中�?
}

void BeforeRunShortGndDetect(void)
{
    Ulong m_Period;
    DisableDrive();
//  gBforeRunPhaseLose.ShortGndDetectFlag = 0;
    if ((gShortGnd.ocFlag != 0) ||
            (gBforeRunPhaseLose.OverFlag == 1) ||
//  (gBforeRunPhaseLose.maxIU > (30 * 32))||
            //(gUDC.uDC > gShortGnd.BaseUDC + 650)|| // 暂时删除母线电压判断条件
            (gCBCProtect.CBCIntFlag == 1))//wujian 追波限流�?�?  我们没有此中�? 所以�?�信号一直为0
    {

        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);//all off
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
        gError.ErrorCode.all |= ERROR_SHORT_EARTH;
        outputloseCode = 15;

        gMainStatus.StatusWord.bit.ShortGndOver = 1;
        gBforeRunPhaseLose.CheckOverFlag = 1;
        if (abs(gShortGnd.ShortCur) > (30 * 32) ||
                (gBforeRunPhaseLose.OverFlag == 1))
        {
            gError.ErrorInfo[2].bit.Fault4 = 3; //�?件过�?
        }
        /*if(gUDC.uDC > gShortGnd.BaseUDC + 650)  暂时删除母线电压判断条件
        {
            gError.ErrorInfo[2].bit.Fault4 = 4; //�?件过�?
        }*/
        if (1 == gShortGnd.ocFlag)
        {
            gError.ErrorInfo[2].bit.Fault4 = 1; //�?件过�?,�?件优先级�?
        }
        if (2 == gShortGnd.ocFlag) //wujian 我们没有�?件过压信�?
        {
            // gError.ErrorInfo[2].bit.Fault4 = 2; //�?件过�?
            gError.ErrorInfo[2].bit.Fault4 = 3; //wujian 借用变量过流
        }
        gLineCur.ErrorShow = gBforeRunPhaseLose.maxIU;
        ShortGndCheckOk = 0;
        // gMainStatus.SubStep = 4;                 // �?定�?�地�?�?�?, 直接结束检测并进�?��?�位
    }
    else                                            // 没有对地�?�?, 按输出缺相�?�测配�?寄存�?, 进入下一步�?��?
    {
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);//all off
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
        // gMainStatus.SubStep++;
        ShortGndCheckOk = 1;
    }
    gBforeRunPhaseLose.ShortGndDetectFlag = 0;
}


void BeforeRunOutputPhaseLoseDetect(void)
{
    Ulong m_Period;

    if (gBforeRunPhaseLose.UWPhaseLoseFlag == 0 && UVphaseCheck_Over)
    {
        UVphaseCheck_Over = 0;
        gLosePhase_maxCur[0] = gBforeRunPhaseLose.maxIU;
        gBforeRunPhaseLose.maxIU = 0;
        gBforeRunPhaseLose.maxIV = 0;
        gBforeRunPhaseLose.maxIW = 0;
        gBforeRunPhaseLose.Counter = 0;
        gBforeRunPhaseLose.UWPhaseLoseFlag = 1;
        gBforeRunPhaseLose.OverFlag = 0;
        if ((gBforeRunPhaseLose.OverFlag != 1) &&
                (gShortGnd.ocFlag == 0) &&
                (gMainStatus.ErrFlag.bit.OvCurFlag == 0))
        {
            EnableDrive();
        }

        m_Period = __HAL_TIM_GET_AUTORELOAD(&htim1);//A+ C-
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, m_Period);
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, m_Period);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);

    }
    else// if(gBforeRunPhaseLose.UWPhaseLoseFlag == 1)
    {
        if ((gBforeRunPhaseLose.OverFlag == 1) && //  变�?�器三相输出电流均满足电流阀值则认为�?缺相
                (gBforeRunPhaseLose.maxIU > gBforeRunPhaseLose.CurComperCoff) &&
                 (gBforeRunPhaseLose.UWPhaseLoseFlag == 1))
        {
            gBforeRunPhaseLose.CheckOverFlag = 1;
        }
    }
}



void ShortGnd_ADC_Over_isr(void) //interrupt
{
    u16 PumpI;

    GetCurrentInfo();
    ShortGnd_PhaseLoseICal(); //需要调�?

}
int32_t gShortGnd_Cur[2][32] = {0};

uint8_t gShortGnd_index = 0;
void ShortGnd_PhaseLoseICal(void)
{
//  gCpuTime.ADCIntBase = GetTime();
    s32 OverCur;

    if (gBforeRunPhaseLose.ShortGndDetectFlag == 1)  // 对地�?�?检�?
    {
        OverCur = (gIUVWQ24.U >> 12) + (gIUVWQ24.V >> 12);
        OverCur = OverCur + (gIUVWQ24.W >> 12);
        gBforeRunPhaseLose.CurTotal = abs((s16)OverCur);                    // 求三相电流之�?, Q12, pu
        gBforeRunPhaseLose.maxIU    = abs(gIUVWQ24.U >> 12);
        
    }
    else        // 输出缺相检�?
    {
        gBforeRunPhaseLose.maxIU    = Max(gBforeRunPhaseLose.maxIU, abs(gIUVWQ24.U >> 12));     // U相最大电�?
        gBforeRunPhaseLose.CurTotal = gBforeRunPhaseLose.maxIU;
    }
    
    if(gBforeRunPhaseLose.UWPhaseLoseFlag)
    {
        // if (LL_TIM_IsEnabledAllOutputs(TIM1))
        // {
        //     if (gShortGnd_index >= 31)
        //     {
        //         gShortGnd_index = 0;
        //     }
        //     gShortGnd_Cur[0][gShortGnd_index] = gBforeRunPhaseLose.maxIU;
        //     gShortGnd_Cur[1][gShortGnd_index] = gBforeRunPhaseLose.CurTotal;
            
        //     gShortGnd_Cur[0][31] = (gShortGnd_Cur[0][31] > gBforeRunPhaseLose.maxIU) ? gShortGnd_Cur[0][31] : gBforeRunPhaseLose.maxIU;
        //     gShortGnd_Cur[1][31] = (gShortGnd_Cur[1][31] > gBforeRunPhaseLose.CurTotal) ? gShortGnd_Cur[1][31] : gBforeRunPhaseLose.CurTotal;
        //     gShortGnd_index++;
        // }
    }

    if ((gBforeRunPhaseLose.maxIU > gBforeRunPhaseLose.CurComperCoff)       // 对于对地�?�?检�?, U相电流大�?50%, 且三相电流之和大�?50%, 认为对地�?�?
            && (gBforeRunPhaseLose.CurTotal > gBforeRunPhaseLose.CurComperCoff))   // 对于输出缺相检�?, U相最大电流大�?50%,
    {
        DisableDrive();

        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
        gBforeRunPhaseLose.OverFlag = 1;
        gShortGnd.ocFlagSave = 3;
        
        if (gBforeRunPhaseLose.ShortGndDetectFlag == 1)
            BeforeRunShortGndDetect();//active err bit shortgound
    }
    else if ((gBforeRunPhaseLose.maxIU > gBforeRunPhaseLose.CurComperCoffLimit)
             && (gBforeRunPhaseLose.CurTotal < (gBforeRunPhaseLose.CurComperCoffLimit >> 1))
             && (gBforeRunPhaseLose.ShortGndDetectFlag == 1))            // 对地�?�?检�?, 当�?�测到U相电流大�?80%，而三相和小于40%时不再发波�?��?
    {
        DisableDrive();

        gShortGnd.ocFlag = 2;
        gShortGnd.ocFlagSave = gShortGnd.ocFlag;
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
        CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);

    }
}
//------------------------------------------------------------------
/*************************************************************
    输出掉载处理

在运行状态，电流小于电机额定电流5%，并持续100ms认为�?掉载
同�?�机不做掉载保护
*************************************************************/
void LoadLoseDetect(void)
{
    Uint m_Limit, m_LimitFreq;

    //掉载判断阀值为电机额定电流�?5%�?
    m_Limit = (gLoadLose.ChkLevel * (Ulong)gMotorInfo.CurrentGet) / gMotorInfo.Current << 2; // *4096/1000
    m_Limit = (long)m_Limit * 1447L >> 10;              // * sqrt(2)
    m_Limit = (m_Limit < 20) ? 20 : m_Limit;
    m_LimitFreq = ((Ulong)gMotorInfo.FreqPer * 1638) >> 14;

    if ((abs(gMainCmd.FreqSyn) < m_LimitFreq)            //电机额定频率10%以下不�?��?
            || (gMainCmd.Command.bit.StartDC == 1)
            || (gMainCmd.Command.bit.StopDC  == 1)              //直流制动状态不检�?
            || (gMainStatus.RunStep != STATUS_RUN)              //非运行状态不检�?
            || (abs(gIUVWQ12.U) >= m_Limit)                     //任何一相电流大�?200 (5%) 认为不掉�?
            || (abs(gIUVWQ12.V) >= m_Limit)
            || (abs(gIUVWQ12.W) >= m_Limit)
       )
    {
        gLoadLose.ErrCnt = 0;
        gMainStatus.StatusWord.bit.OutOff = 0;
        return;
    }

    gLoadLose.ErrCnt++;
    if ((gLoadLose.ErrCnt << 1) > (gLoadLose.ChkTime * 100)) // 掉载检出时间确�?
        //if(gLoadLose.ErrCnt > 50)
    {
        gLoadLose.ErrCnt = 50;
        gError.ErrorCode.all |= ERROR_LOAD_LOST;
        gMainStatus.StatusWord.bit.OutOff = 1;
    }
}

//------------------------------------------------------------------
/*************************************************************
    输出IGBT保护处理
*************************************************************/
void IgbtBreakeDetect(void)
{
    Ulong m_Period = 0;

    if ((rt_pin_read(F_IPM_INV_PIN) == 0)
            &&
            ((rt_pin_read(F_HD_PIN) == 0) || (gIgbtBreake.ResetCnter != 0))
       )
    {
        m_Period = __HAL_TIM_GET_AUTORELOAD(&htim1);
        gError.ErrorCode.all |= ERROR_SHORT_BRAKE;

        if ((gIgbtBreake.ResetState == 0)
                && (gIgbtBreake.ResetDelayTick < (10000))
           )
        {
            DisableDrive();
            gIgbtBreake.ResetDelayTick++;
        }
        else
        {
            switch (gIgbtBreake.ResetState)
            {
            case 0:
                DisableDrive();
                if ((ERROR_LEVEL_NO_ERROR != errorAttribute.bit.level)
                        && (gIgbtBreake.ResetCnter < 5))
                {
                    gIgbtBreake.ResetState = 1;
                    gIgbtBreake.ResetOver = 0;
                    gIgbtBreake.ResetCnter++;
                    gIgbtBreake.ChnnActiveTick  = 0;
                    break;
                }

                gIgbtBreake.ResetOver = (gIgbtBreake.ResetCnter >= 5) ? 1 : 0;

                break;
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
                EnableDrive();
                //rt_pin_write(CLR_HD_PIN, 0);

                if (gIgbtBreake.ResetState == 1)
                {
                    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, m_Period / 200);
                    SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
                }
                else
                {
                    CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
                }

                if (gIgbtBreake.ResetState == 2)
                {
                    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, m_Period * 199 / 200);
                    SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
                }
                else
                {
                    CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
                }

                if (gIgbtBreake.ResetState == 3)
                {
                    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, m_Period / 200);
                    SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
                }
                else
                {
                    CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
                }

                if (gIgbtBreake.ResetState == 4)
                {
                    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, m_Period * 199 / 200);
                    SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
                }
                else
                {
                    CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
                }


                if (gIgbtBreake.ResetState == 5)
                {
                    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, m_Period / 200);
                    SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
                }
                else
                {
                    CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
                }

                if (gIgbtBreake.ResetState == 6)
                {
                    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, m_Period * 199 / 200);
                    SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
                }
                else
                {
                    CLEAR_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
                }

                gIgbtBreake.ResetState++;
                gIgbtBreake.ChnnActiveTick = 0;

                break;


            default:
                DisableDrive();
                //rt_pin_write(CLR_HD_PIN, 1);
                SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
                SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
                SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
                SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
                SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
                SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
                gIgbtBreake.ResetDelayTick = 0;
                gIgbtBreake.ResetState = 0;
                break;
            }
        }
    }
    else
    {
        if (gIgbtBreake.ResetState != 0)
        {
            DisableDrive();
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E);
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE);
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E);
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE);
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E);
            SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE);
            //rt_pin_write(CLR_HD_PIN, 1);
        }
        gIgbtBreake.ResetOver = 1;
        gIgbtBreake.ResetState = 0;
        gIgbtBreake.ResetCnter = 0;
        gIgbtBreake.ResetDelayTick = 0;
    }

}

/*************************************************************
    �����жϴ������򣨿������жϣ���ƽ������

28035 Ҳ�����ǹ�ѹ�жϣ���Ҫ�����жϣ�
28035 �ڷ�����ѹ�󣬻�Ƶ���Ľ�����жϣ� ��֤����û�����Ե�����
*************************************************************/
extern uint8_t lose_phase_uvw;
uint32_t gTim1_break_times = 0;
uint32_t gTim1_irq_times = 0;
uint32_t gbreak_timeout = 3;
void HardWareErrorDeal()    // HHX
{
    gMainStatus.ErrFlag.bit.OvCurFlag = 1;      //发生了过流中断的标志
    gMainCmd.FreqToFunc = gMainCmd.FreqSyn;
    gTim1_irq_times++;

    DIO_NAME_READ_DELAY("BREAKIN") = 500;
    //判断为过流故障
    if ((gMainStatus.RunStep == STATUS_SHORT_GND) ||
            (gMainStatus.RunStep == STATUS_BEFORE_RUN_DETECT))
    {
        gShortGnd.ocFlag = 1;                      //上电对地短路
        gShortGnd.ocFlagSave = gShortGnd.ocFlag;
        DIO_NAME_READ_DELAY("F_IPM") = 500;
    }
    else if ((gError.ErrorCode.all & ERROR_SHORT_BRAKE) != ERROR_SHORT_BRAKE)
    {
        if(nvs_datas.motor.BreakinAutomaticOutput == 1)
            gTim1_break_times++;
        
        lose_phase_uvw = 0;
        if (gMainStatus.RunStep == STATUS_LOW_POWER)   // 欠压下不报过流
        {
            gTim1_break_times= 0;
        }
        else if((gTim1_break_times >= gbreak_timeout) || (nvs_datas.motor.BreakinAutomaticOutput == 0))
        {
            gError.ErrorCode.all |= ERROR_SHORT_BRAKE ;
            DIO_NAME_READ_DELAY("F_IPM") = 500;
        }
    }
    
}
