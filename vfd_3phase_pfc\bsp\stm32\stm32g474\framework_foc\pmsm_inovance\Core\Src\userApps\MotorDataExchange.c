#include "MotorInclude.h"
#include "f_funcCode.h"
#include "f_runSrc.h"

//------------------------------------------------------------
#define CORE_SOFTWARE_VERSION           00
#define CORE_SOFTWARE_VERSION_TEMP      61

#define CORE_NON_STANDARD_VERSION       0
#define CORE_NON_STANDARD_VERSION_TEMP  0

#define CORE_SOFT_TYPE                  0
#define CORE_IS_TEMP_SOFTWARE           0

#define CORE_TEMP_SOFTWARE_NO           0
#define CORE_TEMP_SOFTWARE_VERSION      0


#if(AIRCOMPRESSOR == 1)
    #define     NUM_2ms_MOTOR_TO_FUNC           38
#else
    #define     NUM_2ms_MOTOR_TO_FUNC           36
#endif

#define     NUM_2ms_MOTOR_TO_FUNC_debug     30

#define     NUM_05MS_F2M    14

#define     NUM_2ms_FUNC_TO_MOTOR           120
#define     NUM_2ms_FUNC_TO_MOTOR_debug     30

//------------------------------------------------------------

Uint gCoreSoftWareVesion = (CORE_SOFTWARE_VERSION << 8) + CORE_SOFTWARE_VERSION_TEMP;
Uint gCoreNonStandardVesion = (CORE_NON_STANDARD_VERSION << 6) + CORE_NON_STANDARD_VERSION_TEMP;
Uint gCoreSoftType = (CORE_SOFT_TYPE << 8) + CORE_IS_TEMP_SOFTWARE;
Uint gCoreTmpsoftNoAndVersion = (CORE_TEMP_SOFTWARE_NO << 6) + CORE_TEMP_SOFTWARE_VERSION;
Uint gSoftVersion = SOFT_VERSION;

int16  gSendToMotor05MsDataBuff[NUM_05MS_F2M];


int16  gSendToFunctionDataBuff[NUM_2ms_MOTOR_TO_FUNC + NUM_2ms_MOTOR_TO_FUNC_debug];
int16  gSendToMotor2MsDataBuff[NUM_2ms_FUNC_TO_MOTOR + NUM_2ms_FUNC_TO_MOTOR_debug + 3];

//------------------------------------------------------------


void ParSend05Ms(void)
{

}

void ParGet05Ms(void)
{
    gMainCmd.Command.all            =   gSendToMotor05MsDataBuff[0] ;
    gExtendCmd.all                  =   gSendToMotor05MsDataBuff[1] ;
    gGetParVarable.TuneType         = (TUNE_FLOW_ENUM)gSendToMotor05MsDataBuff[2] ;
    gMotorInfo.MotorType            =   gSendToMotor05MsDataBuff[3] ;
    gMainCmd.FreqSet                =   gSendToMotor05MsDataBuff[4] ;
    gOutVolt.vfSplit                =   gSendToMotor05MsDataBuff[5] ;
    gMotorInfo.Votage               =   gSendToMotor05MsDataBuff[6] ;
    gMotorInfo.CurrentGet           =   gSendToMotor05MsDataBuff[7] ;
    gMotorInfo.Frequency            =   gSendToMotor05MsDataBuff[8] ;
    gVFPar.VFLineType               =   gSendToMotor05MsDataBuff[9] ;
}


void ParSend2Ms(void)
{
    gSendToFunctionDataBuff[0]  =   gMainStatus.StatusWord.all	;
    gSendToFunctionDataBuff[1]	=	gGetParVarable.StatusWord	;
    gSendToFunctionDataBuff[2]	=	gError.ErrorCode.ErrorCodeStruct.ErrorCode1	;
    gSendToFunctionDataBuff[3]	=	gError.ErrorCode.ErrorCodeStruct.ErrorCode2	;
    gSendToFunctionDataBuff[10] =   gMainCmd.FreqToFunc ;


}

uint8_t gShortgnd_cmdinit_flag = 0;
__IO uint8_t shortgnd_check_flag = 0;
uint8_t gAdref_CheckOk;
extern u16 ShortGndCheckOk;
void ParGet2Ms(void)
{
    gMotorInfo.MotorType = funcCode.code.motorParaM1.elem.motorType; //异步电机

    gInvInfo.InvTypeSet = INV_CURR_TABLE_INDEX; //变频器机型, 11
    gExtendCmd.bit.FreqUint = 2; //频率小数点, 0.01A
    gBasePar.MaxFreq = funcCode.code.maxFrq; //最大频率, 0.01Hz
    gMotorInfo.Votage = funcCode.code.motorParaM1.elem.ratingVoltage; //电机额定电压, 0.1V
    gMotorInfo.CurrentGet = funcCode.code.motorParaM1.elem.ratingCurrent; //电机额定电流, 0.01A
    gMotorInfo.Frequency = funcCode.code.motorParaM1.elem.ratingFrq; //电机额定频率, 0.01Hz
    gMotorExtInfo.Rpm = funcCode.code.motorParaM1.elem.ratingSpeed; //电机额定转速, 1rpm

    gMotorExtInfo.R1 = funcCode.code.motorParaM1.elem.statorResistance; //电机定子电阻, 0.001Ω
    gMotorExtInfo.R2 = funcCode.code.motorParaM1.elem.rotorResistance; //电机转子电阻, 0.001Ω
    gMotorExtInfo.LM = funcCode.code.motorParaM1.elem.mutualInductance; //电机互感, 0.1mH
    gMotorExtInfo.L0 = funcCode.code.motorParaM1.elem.leakInductance; //电机漏感, 0.01mH
    gMotorExtInfo.I0 = funcCode.code.motorParaM1.elem.zeroLoadCurrent; //电机空载电流, 0.01A

    gBasePar.FcSetToMotor = funcCode.code.carrierFrq; //5.0kHz
    gInvInfo.CurrentCoff = funcCode.code.curJudgeCoeff; //电流矫正系数1.000
    gInvInfo.UDCCoff = funcCode.code.volJudgeCoeff; //电压矫正系数1.000

    gMotorExtInfo.RsPm = funcCode.code.motorParaM1.elem.pmsmRs; //定子电阻, 0.001Ω
    gMotorExtInfo.LD = funcCode.code.motorParaM1.elem.pmsmLd; //直轴电感, 0.01mH
    gMotorExtInfo.LQ = funcCode.code.motorParaM1.elem.pmsmLq; //交轴电感, 0.01mH
    gMotorExtInfo.BemfVolt = funcCode.code.motorParaM1.elem.pmsmCoeff; //反电势,258.0V

    gIPMInitPos.InitTestFlag =  funcCode.code.vcparaM1.startPositionCheckAble;     // F2-25初始位置是否检测 WJ 0=disable 1=enable

    // xiou: 停机态转速反馈时，转矩限制预防性减小
//    if (!INVERTER_IS_RUN && vfd.bit.close_detect && (funcCode.code.vcParaM1.spdCtrlDriveTorqueLimit > 300)) 
//    {
//        gVCPar.PosVCTorqueLim = 300;
//        gVCPar.NegVCTorqueLim = 300;
//    }
//    else if ((gVCPar.PosVCTorqueLim == 300) && (vfd.motor_run_sec < 2))
//    {
//        gVCPar.PosVCTorqueLim = 300;
//        gVCPar.NegVCTorqueLim = 300;
//    }
//    else
    {
        gVCPar.PosVCTorqueLim = funcCode.code.vcParaM1.spdCtrlDriveTorqueLimit;
        gVCPar.NegVCTorqueLim = funcCode.code.vcParaM1.spdCtrlDriveTorqueLimit;
    }

    gVCPar.ASRKpHigh = funcCode.code.vcParaM1.vcSpdLoopKp2;
    gVCPar.ASRKpLow = funcCode.code.vcParaM1.vcSpdLoopKp1;
    gVCPar.ASRTIHigh = funcCode.code.vcParaM1.vcSpdLoopTi2;
    gVCPar.ASRTILow = funcCode.code.vcParaM1.vcSpdLoopTi1;
    gVCPar.ASRSwitchHigh = funcCode.code.vcParaM1.vcSpdLoopChgFrq2;
    gVCPar.ASRSwitchLow = funcCode.code.vcParaM1.vcSpdLoopChgFrq1;


    gVCPar.AcrImKp = funcCode.code.vcParaM1.mAcrKp;
    gVCPar.AcrImKi = funcCode.code.vcParaM1.mAcrKi;
    gVCPar.AcrItKp = funcCode.code.vcParaM1.tAcrKp;
    gVCPar.AcrItKi = funcCode.code.vcParaM1.tAcrKi;

    gInvInfo.LowUdcCoff = funcCode.code.uvPoint;

    gPmsmRotorPosEst.SvcIdMaxForLowSpeed = funcCode.code.vcparaM1.PmsmSvcIdMaxLowSpeed;       // F2-36 同步机SVC初始励磁电流限幅

    gPmsmRotorPosEst.Ka = funcCode.code.vcparaM1.PmsmSvcKa;                  // f2-35 同步机SVC速度估算积分增益
    gPmsmRotorPosEst.Kb = funcCode.code.vcparaM1.PmsmSvcKb;                  // F2-34 同步机SVC速度估算比例增益
    gPmsmRotorPosEst.SvcSpeedLpfTs = funcCode.code.vcparaM1.PmsmSvcSpeedLpfTs;          // F2-33 同步机SVC速度滤波级别

    gGetParVarable.TuneType = tuneCmd;

    gLoadLose.ChkLevel           =  funcCode.code.loseLoadLevel ;

    gLoadLose.ChkTime            = (funcCode.code.outPhaseLossProtect == 0) ? 0xFFFFFFFF : funcCode.code.loseLoadTime    ;
    
    //----------------------------------wujian
    // 辅助命令字(2ms)
    //  dspSubCmd.bit.outPhaseLossProtect = funcCode.code.outPhaseLossProtect%10;  // 输出缺相保护(个位)
    //  dspSubCmd.bit.outPhaseLossProtect_beforeRun = funcCode.code.outPhaseLossProtect/10;  // 运行前输出缺相保护(十位)
    //  dspSubCmd.bit.inPhaseLossProtect = funcCode.code.inPhaseLossProtect%10; // 输入缺相保护
    //  dspSubCmd.bit.contactorMode = funcCode.code.inPhaseLossProtect/10;      // 接触器吸合保护
    //  dspSubCmd.bit.overloadMode = funcCode.code.overloadMode;                // 电机过载保护
    //  dspSubCmd.bit.loseLoadProtectMode = funcCode.code.loseLoadProtectMode;  // 输出掉载保护使能标志
    //  dspSubCmd.bit.poffTransitoryNoStop = funcCode.code.pOffTransitoryNoStop;// 瞬停不停
    //  //dspSubCmd.bit.overModulation = funcCode.code.overModulation;            // 过调制使能
    //  //dspSubCmd.bit.fieldWeak = funcCode.code.fieldWeak;                      // 弱磁控制
    //  dspSubCmd.bit.cbc = funcCode.code.cbcEnable;                            // 逐波限流使能
    //  //dspSubCmd.bit.narrowPulseMode = funcCode.code.narrowPulseMode;          // 窄脉冲控制选择
    //  //dspSubCmd.bit.currentSampleMode = funcCode.code.currentSampleMode;      // 电流检测滤波（剔除毛刺）选择
    //  dspSubCmd.bit.varFcByTem = funcCode.code.varFcByTem;                    // 载波频率随温度调整，MD280一直有效
    //  //dspSubCmd.bit.pmsmInitPosNoSame = funcCode.code.pmsmInitPosNoSame;
    //  //dspSubCmd.bit.pmsmZeroPosBig = funcCode.code.pmsmZeroPosBig;
    //  gSendToMotor2MsDataBuff[0]=dspSubCmd.all;//wujian 匹配数据类型
    // 辅助命令字(2ms)

    if (((gAdref_CheckOk)// 如果ADREF检测正常，则使用功能码设置的参数
    && !(vfd.diag.dc_98 || vfd.diag.dc_99 || vfd.diag.dc_100)
    && (VFD_INPUT_AC || VFD_INPUT_DC)
    && vfd.bit.ad_init
    && (vfd.filter_ad.ac_vout_u < 50) 
    && (vfd.filter_ad.ac_vout_v < 50)
    && (vfd.filter_ad.ac_vout_w < 50)
    )
    ||
    INVERTER_IS_RUN)
    {  
        gSubCommand.bit.OutputLostBeforeRun = funcCode.code.outPhaseLossProtect / 10; //F9-13 运行前输出缺相保护(十位)
        gExtendCmd.bit.ShortGnd = funcCode.code.shortCheckMode % 10; //F9-07 上电对地短路保护功能(个位)

        if(gExtendCmd.bit.ShortGnd && !ShortGndCheckOk)
            gExtendCmd.bit.ShortGndBeforeRun = 1; 
        else
            gExtendCmd.bit.ShortGndBeforeRun = funcCode.code.shortCheckMode / 10; //F9-07运行前对地短路检测标志(十位)
         
    }
    else
    {
        gSubCommand.bit.OutputLostBeforeRun = 0; //F9-13 运行前输出缺相保护(十位)
        gExtendCmd.bit.ShortGnd = 0; //F9-07 上电对地短路保护功能(个位)
        gExtendCmd.bit.ShortGndBeforeRun = 0; //F9-07运行前对地短路检测标志(十位)
    }

    if( (gAdref_CheckOk)// 如果ADREF检测正常，则使用功能码设置的参数
    && (VFD_INPUT_AC || VFD_INPUT_DC)
    && vfd.bit.ad_init)
        gShortgnd_cmdinit_flag = 1;
            
    gSubCommand.bit.OutputLost = funcCode.code.outPhaseLossProtect % 10; //F9-13 输出缺相保护(个位)

    gSubCommand.bit.MotorOvLoad = funcCode.code.overloadMode;                // F9-00  电机过载保护选择
    gComPar.MotorOvLoad         =  funcCode.code.overloadGain;                // F9-01  电机过载保护增益;
    gOverLoad.OverLoadPreventEnable = funcCode.code.errorAction[1] / 100 % 10; // F9-48 百位:变频器过载选择 0:自由停机 1:降额运行
    gMainCmd.XiuMianFreq           =   funcCode.code.lowerFrq;                   // F0-14 输出下限频率
    gComPar.PerMotorOvLoad =   funcCode.code.foreOverloadCoef;            // F9-02  电机过载预警系数
    /*************以下需要特别注意 需增加功能码变量******************* */
    //  gPmsmRotorPosEst.FcLow = funcCode.code.PmsmSvcFcLow;               // F2-37 同步机SVC最低载波频率
    gPmsmRotorPosEst.FcLow = funcCode.code.vcparaM1.PmsmSvcFcLow;               // F2-37 同步机SVC最低载波频率//
    /********************************************** */
    //wujian 已屏蔽该功能码 FF-04
    gInvInfo.TempType = funcCode.code.tempCurve;   // FF-04  温度曲线默认值 1  // 50   温度曲线选择
    gSubCommand.bit.VarFcByTem = funcCode.code.varFcByTem; // // F0-16  载波频率随温度调整

    //  gPmParEst.IsKpK=4;//funcCode.code.PmParEstIsKpK;    wujian 需要增加该功能码          // f2-30 调谐时电流环KP
    //  gPmParEst.IsKiK=1;//funcCode.code.PmParEstIsKiK;     wujian 需要增加该功能码               // F2-31调谐时电流环KI
    gVFPar.ovGain   = funcCode.code.ovGain;                         // 10  F9-03 过压失速增益
    gVFPar.ovPoint  = funcCode.code.ovPoint;                        // 11  F9-04 过压失速保护电压
    gVFPar.ocGain   = funcCode.code.ocGain;                         // 12  F9-05 过流失速增益
    gVFPar.ocPoint  = funcCode.code.ocPoint;                        // 13  F9-06 过流失速保护电流

    gComPar.StartDCBrakeCur      =  gSendToMotor2MsDataBuff[38] ;
    gComPar.StopDCBrakeCur       =  gSendToMotor2MsDataBuff[39] ;
    gComPar.BrakeCoff            =  gSendToMotor2MsDataBuff[40] ;

    gFlyingStart.KpRatio                    = gSendToMotor2MsDataBuff[108];
	gFlyingStart.KiRatio                    = gSendToMotor2MsDataBuff[109];
	gFlyingStart.CurLimitAdj                = gSendToMotor2MsDataBuff[110];
    gExtendCmd.bit.SpeedSearch              = dspMainCmd1.bit.speedTrack;
    gPmParEst.IsKpK = funcCode.code.vcparaM1.PmParEstIsKpK; // wujian 需要增加该功能码          // f2-30 调谐时电流环KP
    gPmParEst.IsKiK = funcCode.code.vcparaM1.PmParEstIsKiK; //  wujian 需要增加该功能码               // F2-31调谐时电流环KI
    //====vf
    gVFPar.VFTorqueUpLim = funcCode.code.boostCloseFrq;             // 28   VF提升截止频率  精度 0.01  ===2000==20HZ
    gVFPar.VFTorqueUp = funcCode.code.torqueBoost;                // F3-01  转矩提升 60=比如提升60%
}

void ParSendTune(void)
{

}
