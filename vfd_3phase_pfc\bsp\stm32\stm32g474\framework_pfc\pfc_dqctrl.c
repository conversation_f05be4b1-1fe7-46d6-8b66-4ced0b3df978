
/***************************************************************************

Copyright (C), 2022-2022, BSM Tech. Co., Ltd.

* @file           pfc_dqctrl.c
* <AUTHOR> @version        V0.0.1
* @date           2022-09-06
* @brief

History:          // Revision Records

       <Author>             <time>         <version >               <desc>



***************************************************************************/
#include "pfc_dqctrl.h"
#include "arm_math.h"
#include "pfc_sdpll.h"
#include "pfc_pwm.h"
#include "app_pfcctrl.h"
#include "uapp.h"
void High_ErrCheck(void);
float __fsat(float val, float max, float min);

extern uint16_t pfc_adc_value[8];

tstCONTROLVAR PFCVAR;

static void pfc_pi_coeff_update(float ratio,float Iref)
{
    static float save_ratio = 0;
    float Ks,TS,vdc; 
    
    if(save_ratio == ratio) return;

    save_ratio = ratio;
    Ks = 1;                         //一般用电机电压/电机电流

    TS = DPLL3P.SysPar.Ts / ratio;  // 快慢调节采样,分母越大,调节越快

    vdc = 650.0f;       // 母线电压,实际就是PFCVAR.Voutd_coeff
    
    float Tv = 5*(TS+3*TS);
    PFCVAR.PIVbus.Kp = 4*DPLL3P.SysPar.C/(TS+3*TS)*0.08f/Iref ;        //根据实际*0.08
    PFCVAR.PIVbus.Ki = PFCVAR.PIVbus.Kp/Tv*DPLL3P.SysPar.Ts/PFCVAR.PIVbus.Kp/Iref;

    PFCVAR.PIId.Kp = 6*DPLL3P.SysPar.L/(15*TS*Ks)/vdc;
    PFCVAR.PIId.Ki = 6*DPLL3P.SysPar.L/(112.5f*TS*TS*Ks)*DPLL3P.SysPar.Ts/PFCVAR.PIId.Kp/vdc;
    
    PFCVAR.PIIq.Kp = PFCVAR.PIId.Kp;
    PFCVAR.PIIq.Ki = PFCVAR.PIId.Ki;
    
    
}


void InitDqctrl(void)
{
    
    memset(&PFCVAR, 0, sizeof(PFCVAR));

    PFCVAR.PR6para.kpc = 0.0038f;
    PFCVAR.PR6para.krc = 0.26f;
    PFCVAR.PR6para.wrc = 3.1415926535897932384626f;

    PFCVAR.SVPWM.CMP[0][1] = 0; //1;
    PFCVAR.SVPWM.CMP[0][2] = 1; //0;
    PFCVAR.SVPWM.CMP[0][3] = 2; //2;
    PFCVAR.SVPWM.CMP[0][4] = 2; //2;
    PFCVAR.SVPWM.CMP[0][5] = 1; //1;
    PFCVAR.SVPWM.CMP[0][6] = 0; //0;

    PFCVAR.SVPWM.CMP[1][1] = 1;
    PFCVAR.SVPWM.CMP[1][2] = 0;
    PFCVAR.SVPWM.CMP[1][3] = 0;
    PFCVAR.SVPWM.CMP[1][4] = 1;
    PFCVAR.SVPWM.CMP[1][5] = 2;
    PFCVAR.SVPWM.CMP[1][6] = 2;

    PFCVAR.SVPWM.CMP[2][1] = 2;
    PFCVAR.SVPWM.CMP[2][2] = 2;
    PFCVAR.SVPWM.CMP[2][3] = 1;
    PFCVAR.SVPWM.CMP[2][4] = 0;
    PFCVAR.SVPWM.CMP[2][5] = 0;
    PFCVAR.SVPWM.CMP[2][6] = 1;

    PFCVAR.SVPWM.t12[0][1] = 1; //1;//2;
    PFCVAR.SVPWM.t12[0][2] = 4; //5;//1;
    PFCVAR.SVPWM.t12[0][3] = 0; //0;//5;
    PFCVAR.SVPWM.t12[0][4] = 3; //4;//3;
    PFCVAR.SVPWM.t12[0][5] = 2; //2;//0;
    PFCVAR.SVPWM.t12[0][6] = 5; //3;//4;

    PFCVAR.SVPWM.t12[1][1] = 0; //0;//1;
    PFCVAR.SVPWM.t12[1][2] = 5; //4;//3;
    PFCVAR.SVPWM.t12[1][3] = 2; //2;//0;
    PFCVAR.SVPWM.t12[1][4] = 4; //3;//2;
    PFCVAR.SVPWM.t12[1][5] = 1; //1;//4;
    PFCVAR.SVPWM.t12[1][6] = 3; //5;//5;

    PFCVAR.SVPWM.SN[1] = 2;
    PFCVAR.SVPWM.SN[2] = 6;
    PFCVAR.SVPWM.SN[3] = 1;
    PFCVAR.SVPWM.SN[4] = 4;
    PFCVAR.SVPWM.SN[5] = 3;
    PFCVAR.SVPWM.SN[6] = 5;

   

    PFCVAR.PIId.Ref = 0.0f;
    PFCVAR.PIId.Umax = 1.0f;
    PFCVAR.PIId.Umin = -1.0f; //donot change,otherwise phase lock failed 
    PFCVAR.PIId.Out = 0.0f;
    PFCVAR.PIId.i1 = 0.0f;
    PFCVAR.PIId.ui = 0.0f;
    PFCVAR.PIId.up = 0.0f;
    PFCVAR.PIId.v1 = 0.0f;

    
    PFCVAR.PIIq.Ref = 0.0f;
    PFCVAR.PIIq.Umax = 1.0f;
    PFCVAR.PIIq.Umin = -1.0f; //donot change,otherwise phase lock failed 
    PFCVAR.PIIq.Out = 0.0f;
    PFCVAR.PIIq.i1 = 0.0f;
    PFCVAR.PIIq.ui = 0.0f;
    PFCVAR.PIIq.up = 0.0f;
    PFCVAR.PIIq.v1 = 0.0f;

    PFCVAR.PIVbus.Ref = 0.0f;
    PFCVAR.PIVbus.Umax = 1.0f;
    PFCVAR.PIVbus.Umin = -1.0f;
    PFCVAR.PIVbus.Out = 0.0f;
    PFCVAR.PIVbus.i1 = 0.0f;
    PFCVAR.PIVbus.ui = 0.0f;
    PFCVAR.PIVbus.up = 0.0f;
    PFCVAR.PIVbus.v1 = 0.0f;
    
    PFCVAR.Voutd_coeff = 650.0f;
    PFCVAR.Voutq_coeff = 650.0f;
    
    /*
        4.242A pp  ==  3.00A rms
        3.535A pp  ==  2.50A rms
        2.828A pp  ==  2.00A rms
        2.121A pp  ==  1.50A rms
        1.414A pp  ==  1.00A rms
    */
    
    #ifdef FOUT_QUADRANT
    /* 四象限 利用PTU 设置 被测机母线650V  50hz 输出440v ，四象限机母线 580V ，输入容量实测3.1KVA */
    pfc.debug_iref = 5.0f*1.414f;
    #endif
    
    
    #if 0
    /* 20k ,Iref 3A , vdc 450v, ratio 0.15f */
    PFCVAR.PIId.Kp = 0.0133f;
    PFCVAR.PIId.Ki = 0.0200f;
    PFCVAR.PIIq.Kp = 0.0133f;
    PFCVAR.PIIq.Ki = 0.0200f;
    PFCVAR.PIVbus.Kp = 0.0108f;
    PFCVAR.PIVbus.Ki = 0.0025f;
    #else
    
    /*   0.8f
    I -> kp 0.0533333309f , ki 0.106666669f 
    V -> kp 0.0130915819f , ki 0.0040406119f 
    */
    PFCVAR.pid_ratio = 0.55f;
    pfc_pi_coeff_update(PFCVAR.pid_ratio,vfd.pfc_IdRef);  
    
    #endif
}
extern CurExcursion_t gIinExcursion;
static float vbus_folpf_coffee = 0.05f;
float vbus_folpf_coffee_dcdc = 0.01f;
CCMRAM void pfc_qdctrl(void)
{
    DPLL3P.Iac.In.a = ((float)pfc_adc_value[4] * USER_CURRENT_SF - gIinExcursion.ZeroIu);
    DPLL3P.Iac.In.b = ((float)pfc_adc_value[5] * USER_CURRENT_SF - gIinExcursion.ZeroIv);
    DPLL3P.Iac.In.c = ((float)pfc_adc_value[6] * USER_CURRENT_SF - gIinExcursion.ZeroIw);
    
    pfc_pi_coeff_update(PFCVAR.pid_ratio,vfd.pfc_IdRef);  
    //DPLL3P.Iac.In.c = -(DPLL3P.Iac.In.a + DPLL3P.Iac.In.b);

    //float __vbus  = pfc_adc_value[3] * USER_DCBUS_VOLTAGE_SF;
    float __vbus   = pfc_adc_value[3] * USER_DCBUS_VOLTAGE_SF * (1+vfd.SetAdCa.adca[6]*0.001);
    float __vbus2  = pfc_adc_value[7] * USER_DCBUS_VOLTAGE_SF * (1+vfd.SetAdCa.adca[6]*0.001);
    
    vfd.fast_ad.vbus_inv = __vbus;
    
    DPLL3P.Vbus.In  = (1-vbus_folpf_coffee) * DPLL3P.Vbus.In + vbus_folpf_coffee * __vbus;
    vfd.filter_ad.vbus_inv   = F32_FoLPFilter(0.002f,  25.0f,  vfd.filter_ad.vbus_inv, DPLL3P.Vbus.In);
    
    DPLL3P.Vbus.Fil = (1-vbus_folpf_coffee_dcdc) * DPLL3P.Vbus.Fil + vbus_folpf_coffee_dcdc * __vbus2;
    
    DPLL3P.IabIdq.alpha = (0.6666666667f) * (DPLL3P.Iac.In.a - 0.5f * (DPLL3P.Iac.In.b + DPLL3P.Iac.In.c));
    DPLL3P.IabIdq.beta = (0.5773502691f) * (DPLL3P.Iac.In.b - DPLL3P.Iac.In.c);

    DPLL3P.IabIdq.d = DPLL3P.IabIdq.alpha * DPLL3P.AngleCal.cos_theta + DPLL3P.IabIdq.beta * DPLL3P.AngleCal.sin_theta;
    DPLL3P.IabIdq.q = -DPLL3P.IabIdq.alpha * DPLL3P.AngleCal.sin_theta + DPLL3P.IabIdq.beta * DPLL3P.AngleCal.cos_theta;

    if(vfd.ctrl.start_pfc_invmode)
        pfc.debug_iref = vfd.pfc_invmode_IdRef;//
    else
        pfc.debug_iref = vfd.pfc_IdRef;// 
        
    if (pfc.PFC_Runing == 1)
    {
        if (PFCVAR.PIVbus.Ref >= DPLL3P.SysPar.UserVdcref)
        {
            PFCVAR.PIVbus.Ref = DPLL3P.SysPar.UserVdcref;
        }
        else
        {
            PFCVAR.PIVbus.Ref += 0.015f;
        }

        /* proportional term */
        PFCVAR.PIVbus.up = ((PFCVAR.PIVbus.Ref - DPLL3P.Vbus.In) * PFCVAR.PIVbus.Kp);

        /* integral term */
        PFCVAR.PIVbus.ui = (PFCVAR.PIVbus.Out == PFCVAR.PIVbus.v1) ? ((PFCVAR.PIVbus.Ki * PFCVAR.PIVbus.up) + PFCVAR.PIVbus.i1) : PFCVAR.PIVbus.i1;
        PFCVAR.PIVbus.i1 = PFCVAR.PIVbus.ui;

        /* control output */
        PFCVAR.PIVbus.v1 = (PFCVAR.PIVbus.up + PFCVAR.PIVbus.ui);
        
        PFCVAR.PIVbus.Out = __fsat(PFCVAR.PIVbus.v1,
                                   PFCVAR.PIVbus.Umax,
                                   PFCVAR.PIVbus.Umin);

        PFCVAR.PIId.Ref = PFCVAR.PIVbus.Out * pfc.debug_iref;
        
        /* proportional term */
        PFCVAR.PIId.up = ((PFCVAR.PIId.Ref - DPLL3P.IabIdq.d) * PFCVAR.PIId.Kp);
        
        /* integral term */
        PFCVAR.PIId.ui = (PFCVAR.PIId.Out == PFCVAR.PIId.v1) ? ((PFCVAR.PIId.Ki * PFCVAR.PIId.up) + PFCVAR.PIId.i1) : PFCVAR.PIId.i1;
        PFCVAR.PIId.i1 = PFCVAR.PIId.ui;
        
        /* control output */
        PFCVAR.PIId.v1 = (PFCVAR.PIId.up + PFCVAR.PIId.ui);
        PFCVAR.PIId.Out = __fsat(PFCVAR.PIId.v1,
                                 PFCVAR.PIId.Umax,
                                 PFCVAR.PIId.Umin);
                                 
        
        /* proportional term */
        {
            PFCVAR.PIIq.up = ((-DPLL3P.IabIdq.q) * PFCVAR.PIIq.Kp);
        }

        /* integral term */
        PFCVAR.PIIq.ui = (PFCVAR.PIIq.Out == PFCVAR.PIIq.v1) ? ((PFCVAR.PIIq.Ki * PFCVAR.PIIq.up) + PFCVAR.PIIq.i1) : PFCVAR.PIIq.i1;
        PFCVAR.PIIq.i1 = PFCVAR.PIIq.ui;

        /* control output */
        PFCVAR.PIIq.v1 = (PFCVAR.PIIq.up + PFCVAR.PIIq.ui);
        PFCVAR.PIIq.Out = __fsat(PFCVAR.PIIq.v1,
                                 PFCVAR.PIIq.Umax,
                                 PFCVAR.PIIq.Umin);
                                 
        

        PFCVAR.PR6d.err0 =   PFCVAR.PIId.Ref - DPLL3P.IabIdq.d;
        PFCVAR.PR6d.out0 =   -(PFCVAR.PR6coeff.a1[PFCVAR.PR6coeff.flg] * PFCVAR.PR6d.out1)
                             - (PFCVAR.PR6coeff.a2[PFCVAR.PR6coeff.flg] * PFCVAR.PR6d.out2)
                             + (PFCVAR.PR6coeff.b0[PFCVAR.PR6coeff.flg] * PFCVAR.PR6d.err0)
                             + (PFCVAR.PR6coeff.b1[PFCVAR.PR6coeff.flg] * PFCVAR.PR6d.err1)
                             + (PFCVAR.PR6coeff.b2[PFCVAR.PR6coeff.flg] * PFCVAR.PR6d.err2);

        PFCVAR.PR6d.out2 = PFCVAR.PR6d.out1;
        PFCVAR.PR6d.out1 = PFCVAR.PR6d.out0;
        PFCVAR.PR6d.err2 = PFCVAR.PR6d.err1;
        PFCVAR.PR6d.err1 = PFCVAR.PR6d.err0;

        PFCVAR.PR6q.err0 =   - DPLL3P.IabIdq.q;
        PFCVAR.PR6q.out0 =   -(PFCVAR.PR6coeff.a1[PFCVAR.PR6coeff.flg] * PFCVAR.PR6q.out1)
                             - (PFCVAR.PR6coeff.a2[PFCVAR.PR6coeff.flg] * PFCVAR.PR6q.out2)
                             + (PFCVAR.PR6coeff.b0[PFCVAR.PR6coeff.flg] * PFCVAR.PR6q.err0)
                             + (PFCVAR.PR6coeff.b1[PFCVAR.PR6coeff.flg] * PFCVAR.PR6q.err1)
                             + (PFCVAR.PR6coeff.b2[PFCVAR.PR6coeff.flg] * PFCVAR.PR6q.err2);

        PFCVAR.PR6q.out2 = PFCVAR.PR6q.out1;
        PFCVAR.PR6q.out1 = PFCVAR.PR6q.out0;
        PFCVAR.PR6q.err2 = PFCVAR.PR6q.err1;
        PFCVAR.PR6q.err1 = PFCVAR.PR6q.err0;

        /* PR12 */
        #if 1
        PFCVAR.PR12d.err0 =   PFCVAR.PIId.Ref - DPLL3P.IabIdq.d;
        PFCVAR.PR12d.out0 =   -(PFCVAR.PR12coeff.a1[PFCVAR.PR12coeff.flg] * PFCVAR.PR12d.out1)
                             - (PFCVAR.PR12coeff.a2[PFCVAR.PR12coeff.flg] * PFCVAR.PR12d.out2)
                             + (PFCVAR.PR12coeff.b0[PFCVAR.PR12coeff.flg] * PFCVAR.PR12d.err0)
                             + (PFCVAR.PR12coeff.b1[PFCVAR.PR12coeff.flg] * PFCVAR.PR12d.err1)
                             + (PFCVAR.PR12coeff.b2[PFCVAR.PR12coeff.flg] * PFCVAR.PR12d.err2);

        PFCVAR.PR12d.out2 = PFCVAR.PR12d.out1;
        PFCVAR.PR12d.out1 = PFCVAR.PR12d.out0;
        PFCVAR.PR12d.err2 = PFCVAR.PR12d.err1;
        PFCVAR.PR12d.err1 = PFCVAR.PR12d.err0;

        PFCVAR.PR12q.err0 =   - DPLL3P.IabIdq.q;
        PFCVAR.PR12q.out0 =   -(PFCVAR.PR12coeff.a1[PFCVAR.PR12coeff.flg] * PFCVAR.PR12q.out1)
                             - (PFCVAR.PR12coeff.a2[PFCVAR.PR12coeff.flg] * PFCVAR.PR12q.out2)
                             + (PFCVAR.PR12coeff.b0[PFCVAR.PR12coeff.flg] * PFCVAR.PR12q.err0)
                             + (PFCVAR.PR12coeff.b1[PFCVAR.PR12coeff.flg] * PFCVAR.PR12q.err1)
                             + (PFCVAR.PR12coeff.b2[PFCVAR.PR12coeff.flg] * PFCVAR.PR12q.err2);

        PFCVAR.PR12q.out2 = PFCVAR.PR12q.out1;
        PFCVAR.PR12q.out1 = PFCVAR.PR12q.out0;
        PFCVAR.PR12q.err2 = PFCVAR.PR12q.err1;
        PFCVAR.PR12q.err1 = PFCVAR.PR12q.err0;
        #endif
        
        /* PR18 */
        #if 1
        PFCVAR.PR18d.err0 =   PFCVAR.PIId.Ref - DPLL3P.IabIdq.d;
        PFCVAR.PR18d.out0 =   -(PFCVAR.PR18coeff.a1[PFCVAR.PR18coeff.flg] * PFCVAR.PR18d.out1)
                             - (PFCVAR.PR18coeff.a2[PFCVAR.PR18coeff.flg] * PFCVAR.PR18d.out2)
                             + (PFCVAR.PR18coeff.b0[PFCVAR.PR18coeff.flg] * PFCVAR.PR18d.err0)
                             + (PFCVAR.PR18coeff.b1[PFCVAR.PR18coeff.flg] * PFCVAR.PR18d.err1)
                             + (PFCVAR.PR18coeff.b2[PFCVAR.PR18coeff.flg] * PFCVAR.PR18d.err2);

        PFCVAR.PR18d.out2 = PFCVAR.PR18d.out1;
        PFCVAR.PR18d.out1 = PFCVAR.PR18d.out0;
        PFCVAR.PR18d.err2 = PFCVAR.PR18d.err1;
        PFCVAR.PR18d.err1 = PFCVAR.PR18d.err0;

        PFCVAR.PR18q.err0 =   - DPLL3P.IabIdq.q;
        PFCVAR.PR18q.out0 =   -(PFCVAR.PR18coeff.a1[PFCVAR.PR18coeff.flg] * PFCVAR.PR18q.out1)
                             - (PFCVAR.PR18coeff.a2[PFCVAR.PR18coeff.flg] * PFCVAR.PR18q.out2)
                             + (PFCVAR.PR18coeff.b0[PFCVAR.PR18coeff.flg] * PFCVAR.PR18q.err0)
                             + (PFCVAR.PR18coeff.b1[PFCVAR.PR18coeff.flg] * PFCVAR.PR18q.err1)
                             + (PFCVAR.PR18coeff.b2[PFCVAR.PR18coeff.flg] * PFCVAR.PR18q.err2);

        PFCVAR.PR18q.out2 = PFCVAR.PR18q.out1;
        PFCVAR.PR18q.out1 = PFCVAR.PR18q.out0;
        PFCVAR.PR18q.err2 = PFCVAR.PR18q.err1;
        PFCVAR.PR18q.err1 = PFCVAR.PR18q.err0;
        #endif
        
        
        if(vfd.ctrl.start_pfc_invmode)
        {
            PFCVAR.Vout.d = -(PFCVAR.PIId.Out) * DPLL3P.Vbus.In / 1.732f * 0.666667f;
            PFCVAR.Vout.q = -(PFCVAR.PIIq.Out) * DPLL3P.Vbus.In / 1.732f * 0.666667f;
        }
        else
        {
            //PFCVAR.PR6d.out0 = 0;
            //PFCVAR.PR12d.out0 = 0;
            //PFCVAR.PR18d.out0 = 0;
            
            PFCVAR.Vout.d = -(PFCVAR.PIId.Out+PFCVAR.PR6d.out0+PFCVAR.PR12d.out0+PFCVAR.PR18d.out0) * PFCVAR.Voutd_coeff + DPLL3P.DPLLDDSRF.dpdelpf ;
            PFCVAR.Vout.q = -(PFCVAR.PIIq.Out+PFCVAR.PR6q.out0+PFCVAR.PR12d.out0+PFCVAR.PR18d.out0) * PFCVAR.Voutq_coeff + DPLL3P.DPLLDDSRF.qpdelpf - DPLL3P.SysPar.wL*DPLL3P.DPLLDDSRF.lfo*PFCVAR.PIId.Ref;
           
           
            PFCVAR.L  = DPLL3P.SysPar.wL*DPLL3P.DPLLDDSRF.lfo*PFCVAR.PIId.Ref;
            //PFCVAR.Vout.d =  DPLL3P.DPLLDDSRF.dpdelpf ;
            //PFCVAR.Vout.q =  DPLL3P.DPLLDDSRF.qpdelpf - DPLL3P.SysPar.wL*DPLL3P.DPLLDDSRF.lfo*PFCVAR.PIId.Ref;

        }
    }
    else
    {
        if ((pfc.PFC_Start == 1) && (DPLL3P.DPLLDDSRF.freq_stableflag == 1))
        {
            PfcEnPwm();
            pfc.PFC_Runing = 1;
        }
        else
        {
            PfcDisPwm();
            pfc.PFC_Runing = 0;
        }
        Pfcvar_Clear();
        PFCVAR.Vout.d = DPLL3P.DPLLDDSRF.dpdelpf;
        PFCVAR.Vout.q = DPLL3P.DPLLDDSRF.qpdelpf;
		
//		if(!vfd.manual.pfc_vset_mask && (vfd.funcCode.pfcVbusSet != 0))
//			SetSdpll3P_Vref(vfd.funcCode.pfcVbusSet);
    }


    PFCVAR.Vout.alpha = PFCVAR.Vout.d * DPLL3P.AngleCal.cos_theta - PFCVAR.Vout.q * DPLL3P.AngleCal.sin_theta;
    PFCVAR.Vout.beta  = PFCVAR.Vout.d * DPLL3P.AngleCal.sin_theta + PFCVAR.Vout.q * DPLL3P.AngleCal.cos_theta;
    
}

static uint8_t pr_cnter = 0;
void computeDF22_PRcontrollerCoeff(tstPRcoeff *v, float kp, float ki,
                                   float wo, float fs, float wrc)
{
    Trig_Components sincos;
    uint16_t hangle;
    float temp1, temp2, wo_adjusted;
    wo_adjusted = wo / (2.0f * fs);

    if(pr_cnter > 8)
        return;
    
    pr_cnter++;
    
    hangle = wo_adjusted * 65536.0f;
    sincos = Trig_Functions(hangle);

    wo_adjusted = 2.0f * fs * sincos.hSin / sincos.hCos;

    if (v->flg == 0)
    {
        temp1 = 4.0f * fs * fs + wo_adjusted * wo_adjusted + 4.0f * fs * wrc;
        temp2 = 4.0f * ki * wrc * fs / temp1;

        v->a1[1] = ((-8.0f * fs * fs + 2.0f * wo_adjusted * wo_adjusted) / temp1);
        v->a2[1] = ((temp1 - 8.0f * fs * wrc) / temp1);

        v->b0[1] = kp + temp2;
        v->b1[1] = kp * v->a1[1];
        v->b2[1] = -temp2 + kp * v->a2[1];

        v->flg = 1;
    }
    else
    {
        temp1 = 4.0f * fs * fs + wo_adjusted * wo_adjusted + 4.0f * fs * wrc;
        temp2 = 4.0f * ki * wrc * fs / temp1;

        v->a1[0] = ((-8.0f * fs * fs + 2.0f * wo_adjusted * wo_adjusted) / temp1);
        v->a2[0] = ((temp1 - 8.0f * fs * wrc) / temp1);

        v->b0[0] = kp + temp2;
        v->b1[0] = kp * v->a1[0];
        v->b2[0] = -temp2 + kp * v->a2[0];

        v->flg = 0;
    }
}


void Pfcvar_Clear(void)
{
    PFCVAR.PIId.Out = 0;
    PFCVAR.PIId.i1 = 0;
    PFCVAR.PIId.ui = 0;
    PFCVAR.PIId.up = 0;
    PFCVAR.PIId.v1 = 0;

    PFCVAR.PIIq.Out = 0;
    PFCVAR.PIIq.i1 = 0;
    PFCVAR.PIIq.ui = 0;
    PFCVAR.PIIq.up = 0;
    PFCVAR.PIIq.v1 = 0;

    PFCVAR.PIVbus.Out = 0;
    PFCVAR.PIVbus.i1 = 0;
    PFCVAR.PIVbus.ui = 0;
    PFCVAR.PIVbus.up = 0;
    PFCVAR.PIVbus.v1 = 0;

    PFCVAR.PR6q.out2 = 0;
    PFCVAR.PR6q.out1 = 0;
    PFCVAR.PR6q.out0 = 0;
    PFCVAR.PR6q.err2 = 0;
    PFCVAR.PR6q.err1 = 0;
    PFCVAR.PR6q.err0 = 0;

    PFCVAR.PR6d.out2 = 0;
    PFCVAR.PR6d.out1 = 0;
    PFCVAR.PR6d.out0 = 0;
    PFCVAR.PR6d.err2 = 0;
    PFCVAR.PR6d.err1 = 0;
    PFCVAR.PR6d.err0 = 0;
}

void High_ErrCheck(void)
{
    if (
        (fabs(DPLL3P.Iac.In.a) > DPLL3P.Iac.PP)
        || (fabs(DPLL3P.Iac.In.b) > DPLL3P.Iac.PP)
        || (fabs(DPLL3P.Iac.In.c) > DPLL3P.Iac.PP)
    )
    {
        DPLL3P.Iac.cnt++;
        if (DPLL3P.Iac.cnt > 2)
        {
            pfc_fault_stop();
            DPLL3P.Iac.cnt = 0;
            vfd.diag.dc_58 = 1;

            DPLL3P.Iac.actPP.a = DPLL3P.Iac.In.a;
            DPLL3P.Iac.actPP.b = DPLL3P.Iac.In.b;
            DPLL3P.Iac.actPP.c = DPLL3P.Iac.In.c;
            
            rt_kprintf("[Tick %d] ErrCheck ocp :%f %f %f \r\n",rt_tick_get()/2,DPLL3P.Iac.actPP.a,DPLL3P.Iac.actPP.b,DPLL3P.Iac.actPP.c);
        }
    }
    else
    {
        DPLL3P.Iac.cnt = 0;
    }

    if (  fabs(DPLL3P.Vbus.In) > DPLL3P.Vbus.PP )
    {
        DPLL3P.Vbus.cnt++;
        if (DPLL3P.Vbus.cnt > 2)
        {
            pfc_fault_stop();
            DPLL3P.Vbus.cnt = 0;
            vfd.diag.dc_06 = 1;
            DPLL3P.Vbus.actPP = DPLL3P.Vbus.In;
            
            rt_kprintf("[Tick %d] ErrCheck ovp :%f  \r\n",rt_tick_get()/2,DPLL3P.Vbus.actPP);
        }
    }
    else
    {
        DPLL3P.Vbus.cnt = 0;
    }
}

void High_FastErrCheck(void)
{
    if (
        (fabs(DPLL3P.Iac.In.a) > 100.0f)
        || (fabs(DPLL3P.Iac.In.b) > 100.0f)
        || (fabs(DPLL3P.Iac.In.c) > 100.0f)
    )
    {
        DPLL3P.Iac.cnt++;
        if (DPLL3P.Iac.cnt > 1)
        {
             pfc_fault_stop();
            DPLL3P.Iac.cnt = 0;
            vfd.diag.dc_58 = 1;
    
            DPLL3P.Iac.actPP.a = DPLL3P.Iac.In.a;
            DPLL3P.Iac.actPP.b = DPLL3P.Iac.In.b;
            DPLL3P.Iac.actPP.c = DPLL3P.Iac.In.c;

            rt_kprintf("[Tick %d] FastErrCheck ocp :%f %f %f \r\n",rt_tick_get()/2,DPLL3P.Iac.actPP.a,DPLL3P.Iac.actPP.b,DPLL3P.Iac.actPP.c);
        }
    }
    else
    {
        DPLL3P.Iac.cnt = 0;
    }

    if (  fabs(DPLL3P.Vbus.In) > DPLL3P.Vbus.PP )
    {
        DPLL3P.Vbus.cnt++;
        if (DPLL3P.Vbus.cnt > 1)
        {
            pfc_fault_stop();
            DPLL3P.Vbus.cnt = 0;
            vfd.diag.dc_06 = 1;
            DPLL3P.Vbus.actPP = DPLL3P.Vbus.In;
            
            rt_kprintf("[Tick %d] FastErrCheck ovp :%f \r\n",rt_tick_get()/2,DPLL3P.Vbus.actPP);
        }
    }
    else
    {
        DPLL3P.Vbus.cnt = 0;
    }
}



extern int COMP;

#include "finsh.h"
void cmd_pfc_iq(int argc, char **argv)
{
    if(argc >= 2)
    {
        float val = atof(argv[2]);
        if(!rt_strcmp("kp",argv[1]))
        {
            PFCVAR.PIIq.Kp = val;  
            PFCVAR.PIId.Kp = val;  
        }   
        else if(!rt_strcmp("ki",argv[1]))
        {
            PFCVAR.PIIq.Ki = val;
            PFCVAR.PIId.Ki = val;
        } 
        else 
        {
            rt_kprintf("unknow %s %f\r\n",argv[1],val);
            return;
        }
        rt_kprintf("set %s to %f\r\n",argv[1],val);
    }
}
MSH_CMD_EXPORT_ALIAS(cmd_pfc_iq, pfc_iq, change pfc iq coffee);

void cmd_pfc_id(int argc, char **argv)
{
    if(argc >= 2)
    {
        float val = atof(argv[2]);
        if(!rt_strcmp("kp",argv[1]))
        {
            PFCVAR.PIId.Kp = val;  
            PFCVAR.PIIq.Kp = val;  
        }   
        else if(!rt_strcmp("ki",argv[1]))
        {
            PFCVAR.PIId.Ki = val;
            PFCVAR.PIIq.Ki = val;
        } 
        else if(!rt_strcmp("ratio",argv[1]))
        {
            val = __fsat(val,2.0f,0.1f);
            pfc_pi_coeff_update(val,vfd.pfc_IdRef);
        } 
        else if(!rt_strcmp("dead",argv[1]))
        {
            val = __fsat(val,100.0f,0);
            COMP = (int)val;
        } 
        else 
        {
            rt_kprintf("unknow %s %f\r\n",argv[1],val);
            return;
        }
        rt_kprintf("set %s to %f\r\n",argv[1],val);
    }
}
MSH_CMD_EXPORT_ALIAS(cmd_pfc_id, pfc_id, change pfc id coffee);

void cmd_pfc_vbus(int argc, char **argv)
{
    if(argc >= 2)
    {
        float val = atof(argv[2]);
        if(!rt_strcmp("kp",argv[1]))
        {
            PFCVAR.PIVbus.Kp = val;  
        }   
        else if(!rt_strcmp("ki",argv[1]))
        {
            PFCVAR.PIVbus.Ki = val;
        } 
        else if(!rt_strcmp("lpf",argv[1]))
        {
            vbus_folpf_coffee = val;
        }
        else if(!rt_strcmp("voutd",argv[1]))
        {
            PFCVAR.Voutd_coeff = val;
            PFCVAR.Voutq_coeff = val;
        }
        else if(!rt_strcmp("voutq",argv[1]))
        {
            PFCVAR.Voutd_coeff = val;
            PFCVAR.Voutq_coeff = val;
        }
        else if(!rt_strcmp("ch4",argv[1]))
        {
            LL_TIM_OC_SetCompareCH4(TIM8, (int)val);
        }
        else 
        {
            rt_kprintf("unknow %s %f\r\n",argv[1],val);
            return;
        }
        rt_kprintf("set %s to %f\r\n",argv[1],val);
    }
}
MSH_CMD_EXPORT_ALIAS(cmd_pfc_vbus, pfc_vbus, change pfc vbus coffee);


void cmd_pfc_pr(int argc, char **argv)
{
    if(argc >= 2)
    {
        float val = atof(argv[2]);
        
        if(!rt_strcmp("kpc",argv[1]))
        {
            PFCVAR.PR6para.kpc = val; /* default: 0.0038f */
            pr_cnter = 0;
            computeDF22_PRcontrollerCoeff(&PFCVAR.PR6coeff , PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 6,  DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
            computeDF22_PRcontrollerCoeff(&PFCVAR.PR12coeff, PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 12, DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
            computeDF22_PRcontrollerCoeff(&PFCVAR.PR18coeff, PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 18, DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
            
        }   
        else if(!rt_strcmp("krc",argv[1]))
        {
            PFCVAR.PR6para.krc = val; /* default: 0.26f */
            pr_cnter = 0;
            computeDF22_PRcontrollerCoeff(&PFCVAR.PR6coeff , PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 6,  DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
            computeDF22_PRcontrollerCoeff(&PFCVAR.PR12coeff, PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 12, DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
            computeDF22_PRcontrollerCoeff(&PFCVAR.PR18coeff, PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 18, DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
        } 
    }
}
MSH_CMD_EXPORT_ALIAS(cmd_pfc_pr, pfc_pr, change pfc pr);
