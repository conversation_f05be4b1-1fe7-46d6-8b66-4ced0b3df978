#include "f_runSrc.h"
#include "f_frqSrc.h"
#include "f_io.h"
#include "f_comm.h"
#include "f_ui.h"
#include "f_main.h"
#include "MotorParaIDinclude.h"


union DSP_MAIN_COMMAND dspMainCmd;  // 功能传递给性能的主命令字
union DSP_MAIN_COMMAND1 dspMainCmd1;
union DSP_SUB_STATUS dspSubStatus;

union RUN_CMD runCmd;               // 运行命令字
union RUN_FLAG runFlag;             // 运行标识字
enum RUN_STATUS runStatus;              // 运行状态

enum START_RUN_STATUS startRunStatus;   // 启动时的运行状态
enum STOP_RUN_STATUS stopRunStatus;     // 停机时的运行状态

union DSP_SUB_COMMAND dspSubCmd;  //wujian  // 功能传递给性能的辅命令字
union DSP_STATUS dspStatus;         // 性能传递给功能的状态字
Uint16 tuneCmd;
Uint32 runTimeTicker;        // 运行时间计时

int GetRunstatus()
{
    return runStatus;
}

//=====================================================================
//
// 更新运行命令字runCmd
//
// 流程:
//
// 1. 更新运行命令源命令源runSrc。
//
// 2. 根据运行命令给定方式，更新命令字runCmd。
//
// 3. 根据变频器内部逻辑，更新命令字runCmd。
//
//=====================================================================
void UpdateRunCmd(void)
{
	static int tick = 0;
	runCmd.bit.freeStop = 0;
    runCmd.bit.errorReset = 0;


	if(funcCode.code.tuneCmd == 11 || funcCode.code.tuneCmd == 12)
	{
		tuneCmd = funcCode.code.tuneCmd - 7;
		funcCode.code.tuneCmd = 0;
	}

#if 0
	if(!runCmd.bit.otherStop && (gUDC.uDCBigFilter >= 6200))
	{
		if(tick <= 1000)
			tick++;
		else
        {
			if(errorCode != 0)
			commRunCmd = SCI_RUN_CMD_RESET_ERROR;
		else
			commRunCmd = SCI_RUN_CMD_FWD_RUN;
		}
	}
	else if(runCmd.bit.otherStop || (gUDC.uDCBigFilter <= 5500))
	{
		commRunCmd = SCI_RUN_CMD_STOP;
		tick = 0;
	}
#endif

    switch (commRunCmd)
    {
        case SCI_RUN_CMD_FWD_RUN:
        	runCmd.bit.common0 = 1;
            runCmd.bit.dir = FORWARD_DIR;
            break;

        case SCI_RUN_CMD_REV_RUN:
            runCmd.bit.common0 = 1;
            runCmd.bit.dir = REVERSE_DIR;
            break;


        case SCI_RUN_CMD_FREE_STOP:
            runCmd.bit.freeStop = 1;
            //lint -fallthrough     //通讯自由停车也要清除jogCmdPulse和common0

        case SCI_RUN_CMD_STOP:
            runCmd.bit.common0 = 0;
            break;

        case SCI_RUN_CMD_RESET_ERROR:
            runCmd.bit.errorReset = 1;
            break;

        default:
            break;
    }
    commRunCmd = 0;                 // 通讯命令字清零



	//---------------------------------------------------------------
	// 故障后运行方式
	// 自由停机、减速停机、继续运行
		if ((ERROR_LEVEL_FREE_STOP == errorAttribute.bit.level)   // 自由停车
			|| (((runStatus == RUN_STATUS_TUNE) 				 // 调谐 且 存在故障
				|| (dspMainCmd1.bit.speedTrack) 				 // 转速追踪 且存在故障
				)
				&&	errorCode
			   )
			)
		{
			runCmd.bit.freeStop = 1;
			runCmd.bit.common0 = 0;
		}
		else if (ERROR_LEVEL_STOP == errorAttribute.bit.level)	// 按停机方式停机
		{
			runCmd.bit.common0 = 0;
		}
		else if (ERROR_LEVEL_RUN == errorAttribute.bit.level)	// 继续运行
		{
			;
		}


	runCmd.bit.common = 0;

	if(runCmd.bit.common0)
	{
		runCmd.bit.common = 1;
	}
}

//=====================================================================
//
// 1. 根据运行方向,运行方式(点动还是普通运行)，跳跃频率
// 计算设定频率(目标频率frqAim)
// 2. 更性设定频率和运行频率的方向，判断当前是否正在反向(是否在正反转死区内)。
//
// 由于运行方向、点动命令是在命令源处理的，
// 所以该函数应该在命令源中调用。
//
//=====================================================================
void UpdateFrqAim(void)
{
// 判断是否超过上限频率
    if (frqAimTmp >= upperFrq)
    {
        frqAimTmp = upperFrq;
    }
    else if (frqAimTmp <= -(int32_t)upperFrq)
    {
        frqAimTmp = -(int32_t)upperFrq;
    }

// 赋值给frqAim
    frqAim = frqAimTmp;

// 运行方向
    if (FORWARD_DIR != runCmd.bit.dir)
    {
        frqAim = -frqAim;
    }
// 判断设定频率的方向
    if (frqAim > 0)
    {
        runFlag.bit.dir = FORWARD_DIR;
    }
    else if (frqAim < 0)
    {
        runFlag.bit.dir = REVERSE_DIR;
    }
    else
    {
        runFlag.bit.dir = runCmd.bit.dir;
    }
}

uint8_t gFlyStartFlag = 0;
uint8_t gFlyBreakStartFlag = 0;
int32_t tDebugStartFrq[32] = {0};
uint8_t tDebugStartFrqCnt = 0;
int32_t gSaveStartFreq = 0;

void StartRunCtrl(void)
{
	runFlag.bit.jog = 0;

	runFlag.bit.run = 1;
	runFlag.bit.common = 1;
	dspMainCmd.bit.run = 1;


	if (!runCmd.bit.common) 	// 启动时，有停机命令
	{
        if (START_RUN_STATUS_HOLD_START_FRQ == startRunStatus)
            runStatus = RUN_STATUS_STOP;
		else
            runStatus = RUN_STATUS_SHUT_DOWN;
        
        runTimeTicker = 0;  // ticker清零
        
		return;
	}

	 #if 1
    switch (startRunStatus)
    {
         case START_RUN_STATUS_SPEED_TRACK:
            if (FUNCCODE_startMode_SPEED_TRACK == funcCode.code.startMode) // 转速跟踪启动
            {
                {
                    dspMainCmd1.bit.speedTrack = 1;

                    if (dspStatus.bit.speedTrackEnd)    // 转速跟踪完成
                    {
                        dspMainCmd1.bit.speedTrack = 0;
                        frqAimOld4Dir =  frqRun;         // 转速跟踪时也要考虑正反转死区
                        runStatus = RUN_STATUS_NORMAL;
                    }
                    frqTmp = frqRun;                // 更新当前跟踪的频率
                    break;
                } 
            }
            else  if (FUNCCODE_startMode_DIRECT_START == funcCode.code.startMode)
            {
                startRunStatus = START_RUN_STATUS_BRAKE;   // 起动制动
            }
            else   if (FUNCCODE_startMode_FORE_MAG == funcCode.code.startMode)
            {
                //  VF控制模式
                if (gCtrMotorType == ASYNC_VF)
                {
                    startRunStatus = START_RUN_STATUS_BRAKE;   // 起动制动
                }
                else
                {
                    startRunStatus = START_RUN_STATUS_PRE_FLUX;  // 预励磁启动
                }
            }
         // 起动制动
        case START_RUN_STATUS_BRAKE:
            if(startRunStatus == START_RUN_STATUS_BRAKE)
            {
				Uint32 timeoutTick = 0;
				++runTimeTicker;
				
				#include "uapp.h"
				if(vfd.driveMotorType == 2)
				{
					timeoutTick = vfd.startDC_SecTick * 10;
				}
				else
					timeoutTick = funcCode.code.startBrakeTime;

                if((((vfd.acout_freq_folp < -5.0f) && funcCode.code.runDir == 0)
                ||  ((vfd.acout_freq_folp > 5.0f) && funcCode.code.runDir == 1)
                || (vfd.diag.dc_98 || vfd.diag.dc_99 || vfd.diag.dc_100))
                && (timeoutTick == 0))                
                {
                    if(gFlyBreakStartFlag == 0)
                    {
                        tDebugStartFrq[tDebugStartFrqCnt] = vfd.acout_freq_folp;
                        tDebugStartFrqCnt++;
                        if(tDebugStartFrqCnt >= 32)
                        {
                            tDebugStartFrqCnt = 0;
                        }
                        gSaveStartFreq =  vfd.acout_freq_folp;
                    }
                    gFlyBreakStartFlag = 1;
                }
                
                if (gFlyBreakStartFlag == 1)
                {
                    timeoutTick = 10; // 1= 100ms
                }
                
				if ((runTimeTicker >= (Uint32)timeoutTick
                     * (Uint16)(TIME_UNIT_START_BRAKE / RUN_CTRL_PERIOD))
                    )
                {    
                    if(gFlyBreakStartFlag == 1)
                    {
                        //nvs_datas.accumulator.dataU32[54]++;
                    }
                    gFlyBreakStartFlag = 0;
                    runTimeTicker = 0;
                    dspMainCmd.bit.startBrake = 0;  // 清制动命令
                    startRunStatus = START_RUN_STATUS_HOLD_START_FRQ;
                    PrepareParForRun();
                    
                }
                else
                {
                    dspMainCmd.bit.startBrake = 1;  // 置制动命令
                    break;
                }
            }
            
        // 预励磁启动
        case START_RUN_STATUS_PRE_FLUX:
			if(vfd.driveMotorType == 2)
			{
				runStatus = RUN_STATUS_SHUT_DOWN;
				runTimeTicker = 0;  // ticker清零
				return;
			}
			
            if(startRunStatus == START_RUN_STATUS_PRE_FLUX)
            {
                if ((++runTimeTicker >= (Uint32)funcCode.code.startBrakeTime
                     * (Uint16)(TIME_UNIT_START_BRAKE / RUN_CTRL_PERIOD))
    				 )
                {
                    runTimeTicker = 0;
                    dspMainCmd.bit.startFlux = 0;  // 清预励磁命令
                    startRunStatus = START_RUN_STATUS_HOLD_START_FRQ;
                }
                else
                {

                    dspMainCmd.bit.startFlux = 1;  // 置预励磁命令
                    break;
                }
            }
               
        // 起动频率保持    
        case START_RUN_STATUS_HOLD_START_FRQ:
        
            startRunStatus = START_RUN_STATUS_HOLD_START_FRQ;
         // 注意，启动频率保持时间时间为0，则从启动频率开始normal run
            frqTmp = (FORWARD_DIR == funcCode.code.runDir) ? funcCode.code.startFrq : (-(int32)funcCode.code.startFrq);

            if(vfd.acout_freq_folp > 10.0f) 
            {
                if(gFlyStartFlag == 0)
                {
                    tDebugStartFrq[tDebugStartFrqCnt] = vfd.acout_freq_folp;
                    tDebugStartFrqCnt++;
                    if(tDebugStartFrqCnt >= 32)
                    {
                        tDebugStartFrqCnt = 0;
                    }
                    gSaveStartFreq =  vfd.acout_freq_folp;
                }
                frqTmp = vfd.acout_freq_folp*100;//funcCode.code.presetFrq;
                frqTmp = ((frqTmp > 0)&&(frqTmp < 2500) && (funcCode.code.presetFrq >= 2500)) ? 2500: frqTmp;
                frqTmp += 100;
                frqCurAim = frqTmp;  // 更新目标频率
                frqCurAimOld = frqCurAim;
                gFlyStartFlag = 1;
                runTimeTicker =  (Uint32)funcCode.code.startFrqTime
                                    * (Uint16)(TIME_UNIT_START_FRQ_WAIT / RUN_CTRL_PERIOD);
            }

            if (++runTimeTicker >= (Uint32)funcCode.code.startFrqTime
                 * (Uint16)(TIME_UNIT_START_FRQ_WAIT / RUN_CTRL_PERIOD))
            { 
                // FVC启动，从当前频率开始
                // SVC启动，速度跟踪开启则以反馈速度运行
                if ((SYNC_SVC == gCtrMotorType)&&(funcCode.code.startMode == 1))
                {
                    // 从当前电机反馈速度开始启动
                  //  frqTmp = frqFdbTmp;  // frqRun;
                  //frqTmp =  frqRun;
                }
            
                runTimeTicker = 0;
                runStatus = RUN_STATUS_NORMAL;
            }
            else
            {
                frqCurAim = frqTmp;  // 更新目标频率
                frqCurAimOld = frqCurAim;
            }

            break;

        default:
            break;
    }
    #else
	runStatus = RUN_STATUS_NORMAL;
    #endif
}

extern uint8_t gFlyStartFlag;
//=====================================================================
//
// normal运行控制
//
//=====================================================================
void NormalRunCtrl(void)
{
    runFlag.bit.jog = 0;
	runFlag.bit.common = 1;

    if (!runCmd.bit.common) // 运行中有停机命令
    {
        runStatus = RUN_STATUS_STOP;
        return;
    }

    if(gFlyStartFlag)
    {
        AccDecFrqCalc(0, decFrqTime, funcCode.code.accDecSpdCurve);
        if(runFlag.bit.accDecStatus == CONST_SPEED)
        {
            gFlyStartFlag = 0;
        }
    }
    else
        AccDecFrqCalc(accFrqTime, decFrqTime, funcCode.code.accDecSpdCurve);
}

//=====================================================================
//
// 停机控制
//
//=====================================================================
void StopRunCtrl(void)
{
    if (runCmd.bit.common)     // 停机时，有启动命令
    {
        runStatus = RUN_STATUS_NORMAL;
        return;
    }
    gFlyStartFlag = 0;
	frqCurAim = 0;				// 目标频率为0
	AccDecFrqCalc(accFrqTime, decFrqTime, funcCode.code.accDecSpdCurve);
	if ((!frqTmp)	 // 功能给定频率为0，且性能给定的实际转速小于1Hz，关断
//	&& (ABS_INT32(frqRun) < 50)
	)  // 性能反馈小于_
	{
		runStatus = RUN_STATUS_SHUT_DOWN;
	}
}


//=====================================================================
//
// 关断PWM控制
//
//=====================================================================
void ShutDownRunCtrl(void)
{
	dspMainCmd.bit.run = 0;         // dspMainCmd.all &= 0xf330;
	dspMainCmd1.bit.speedTrack = 0;
	dspMainCmd.bit.stopBrake = 0;
	dspMainCmd.bit.startBrake = 0;
	dspMainCmd.bit.startFlux = 0;
	dspMainCmd.bit.accDecStatus = 0;

	runFlag.bit.run = 0;    // 只清一部分标志
	runFlag.bit.common = 0;
	runFlag.bit.jog = 0;
	runFlag.bit.tune = 0;
	runFlag.bit.jogWhenRun = 0;
	runFlag.bit.accDecStatus = 0;
	runFlag.bit.servo = 0;
    runTimeTicker = 0;          // ticker清零
	frqTmp = 0;                 // 运行频率清零
	runStatus = RUN_STATUS_WAIT; // 停机完成，等待再次启动
	
	tuneCmd = 0;
}

void TuneRunCtrl(void)
{
    runFlag.bit.run = 1;
    runFlag.bit.tune = 1;

    dspMainCmd.bit.run = 1;

    if (!runCmd.bit.common)    
    {
		runStatus = RUN_STATUS_STOP; // 停机完成，等待再次启动
    }

	if(gGetParVarable.StatusWord == TUNE_SUCCESS)
	{
		runStatus = RUN_STATUS_STOP; // 停机完成，等待再次启动
		tuneCmd = 0;
		runCmd.bit.common = 0;
		runCmd.bit.common0 = 0;
	}
}



void RunSrcUpdateFrq(void)
{
	if (runCmd.bit.freeStop)   // 故障码，端子自由停车，通讯自由停车，停机方式为自由停车，正在恢复出厂参数。
	{
		runStatus = RUN_STATUS_SHUT_DOWN;
	}
	else if (runCmd.bit.common)
	{
		if (RUN_STATUS_WAIT == runStatus) // 等待启动
		{
			if(tuneCmd)
				runStatus = RUN_STATUS_TUNE;
			else
            {
				runStatus = RUN_STATUS_START;
                startRunStatus= START_RUN_STATUS_INIT;
            }

		}
	}

// 根据运行方向,运行方式(点动还是普通运行),跳跃频率，计算设定频率(目标频率frqAim).
// 包括方向处理.
	UpdateFrqAim();

	frqCurAim = frqAim; 	 // 直流制动之前，就给性能传递

	switch (runStatus)
	{
		case RUN_STATUS_START:	// 启动
			StartRunCtrl();
			break;

		case RUN_STATUS_JOG:	// 点动运行
			break;

		case RUN_STATUS_TUNE:	// 调谐运行
			TuneRunCtrl();
			break;

		case RUN_STATUS_DI_BRAKE_DEC:  // 端子制动前减速
			break;

		case RUN_STATUS_DI_BRAKE:  // 制动
			break;

		case RUN_STATUS_LOSE_LOAD: // 掉载
			break;

		case RUN_STATUS_WAIT:
			break;

		case RUN_STATUS_NORMAL:
			break;

		case RUN_STATUS_STOP:
			break;

		case RUN_STATUS_SHUT_DOWN:
			break;

		default:
			break;
	}

	if (RUN_STATUS_NORMAL == runStatus) // normal run
		NormalRunCtrl();

	if (RUN_STATUS_STOP == runStatus)		// 停机
		StopRunCtrl();

	if (RUN_STATUS_SHUT_DOWN == runStatus)	// shutdown, 关断
		ShutDownRunCtrl();

	frq = frqTmp;
}





